package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.model.dto;

import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-07-07 16:12
 * @description 收入后付费出账MQ消息体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomePostpaidBillingMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 服务账单DO
     */
    private PostpaidProductServiceIncomeBillDO serviceIncomeBillDO;

    /**
     * 订阅信息
     */
    private AccountSubscriptionsVO subscription;

    /**
     * 订阅详情
     */
    private AccountSubscriptionsVO.Detail detail;

    /**
     * 产品信息
     */
    private AccountSubscriptionsVO.Product product;

    /**
     * 具体服务信息
     */
    private AccountSubscriptionsVO.Service service;

    /**
     * 周期信息
     */
    private CyclePeriodResultVO cyclePeriodResultVO;
}
