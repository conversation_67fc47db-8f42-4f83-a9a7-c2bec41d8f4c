package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2025-07-16 16:24
 * @description AbstractOriginalPriceStrategy 测试类
 */
@ExtendWith(MockitoExtension.class)
public class AbstractOriginalPriceStrategyTest {

    private TestableAbstractOriginalPriceStrategy strategy;
    private OriginalPriceCalculateRequest mockRequest;
    private OriginalPriceCalculateResponse mockResponse;

    // 测试用的具体实现类
    private static class TestableAbstractOriginalPriceStrategy extends AbstractOriginalPriceStrategy {
        // 暴露 protected 方法用于测试
        public void testCalculateProportion(OriginalPriceCalculateRequest request, OriginalPriceCalculateResponse response) {
            calculateProportion(request, response);
        }
    }

    @BeforeEach
    void setUp() {
        strategy = new TestableAbstractOriginalPriceStrategy();
        mockRequest = mock(OriginalPriceCalculateRequest.class);
        mockResponse = mock(OriginalPriceCalculateResponse.class);
    }

    /**
     * 测试按比例计算 - 正常场景
     * 场景：7月15日订阅，7月31日结束，当月31天，实际使用17天
     * 预期比例：17/31 ≈ 0.5484
     */
    @Test
    void testCalculateProportion_NormalScenario() {
        System.out.println("=== 测试按比例计算 - 正常场景 ===");
        
        // 设置测试数据
        // 2025年7月15日 00:00:00 到 2025年7月31日 23:59:59
        long startTime = 1752595200000L;      // 2025-07-15 00:00:00
        long endTime = 1754044799999L;        // 2025-07-31 23:59:59
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59
        
        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);
        
        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("100.00");
        BigDecimal discountedPrice = new BigDecimal("90.00");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);
        
        System.out.println("测试数据:");
        System.out.println("- 订阅开始时间: 2025-07-15 00:00:00");
        System.out.println("- 订阅结束时间: 2025-07-31 23:59:59");
        System.out.println("- 周期开始时间: 2025-07-01 00:00:00");
        System.out.println("- 周期结束时间: 2025-07-31 23:59:59");
        System.out.println("- 原始价格: " + originalPrice);
        System.out.println("- 优惠价格: " + discountedPrice);
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证结果
        // 实际使用天数：7月15日到7月31日 = 17天
        // 周期总天数：7月1日到7月31日 = 31天
        // 比例：17/31 ≈ 0.5484
        BigDecimal expectedProportion = BigDecimal.valueOf(17).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);
        
        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 17天");
        System.out.println("- 周期总天数: 31天");
        System.out.println("- 预期比例: " + expectedProportion);
        System.out.println("- 预期原始价格: " + expectedOriginalPrice);
        System.out.println("- 预期优惠价格: " + expectedDiscountedPrice);
        
        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);
        
        System.out.println("✅ 正常场景测试通过");
    }

    /**
     * 测试按比例计算 - 不启用按比例计算
     */
    @Test
    void testCalculateProportion_NotEnabled() {
        System.out.println("=== 测试按比例计算 - 不启用按比例计算 ===");
        
        // Mock request - 不启用按比例计算
        when(mockRequest.isByProportion()).thenReturn(false);
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证不会调用任何设置方法
        verify(mockResponse, never()).setOriginalPrice(any());
        verify(mockResponse, never()).setDiscountedPrice(any());
        
        System.out.println("✅ 不启用按比例计算测试通过");
    }

    /**
     * 测试按比例计算 - 参数不完整
     */
    @Test
    void testCalculateProportion_IncompleteParameters() {
        System.out.println("=== 测试按比例计算 - 参数不完整 ===");
        
        // Mock request - 启用按比例计算但参数不完整
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(null); // 缺少开始时间
        when(mockRequest.getEndTime()).thenReturn(1754044799999L);
        when(mockRequest.getCycleStartTime()).thenReturn(1751299200000L);
        when(mockRequest.getCycleEndTime()).thenReturn(1753891199999L);
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证不会调用任何设置方法
        verify(mockResponse, never()).setOriginalPrice(any());
        verify(mockResponse, never()).setDiscountedPrice(any());
        
        System.out.println("✅ 参数不完整测试通过");
    }

    /**
     * 测试按比例计算 - 实际使用时间无效
     */
    @Test
    void testCalculateProportion_InvalidUsageTime() {
        System.out.println("=== 测试按比例计算 - 实际使用时间无效 ===");
        
        // 设置测试数据 - 开始时间晚于结束时间
        long startTime = 1754044799999L;      // 2025-07-31 23:59:59
        long endTime = 1752595200000L;        // 2025-07-15 00:00:00
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59
        
        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);
        
        System.out.println("测试数据:");
        System.out.println("- 订阅开始时间: 2025-07-31 23:59:59");
        System.out.println("- 订阅结束时间: 2025-07-15 00:00:00");
        System.out.println("- 说明: 开始时间晚于结束时间，实际使用时间无效");
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证不会调用任何设置方法
        verify(mockResponse, never()).setOriginalPrice(any());
        verify(mockResponse, never()).setDiscountedPrice(any());
        
        System.out.println("✅ 实际使用时间无效测试通过");
    }

    /**
     * 测试按比例计算 - 同一天使用
     */
    @Test
    void testCalculateProportion_SameDayUsage() {
        System.out.println("=== 测试按比例计算 - 同一天使用 ===");
        
        // 设置测试数据 - 同一天开始和结束
        long startTime = 1752595200000L;      // 2025-07-15 00:00:00
        long endTime = 1752681599999L;        // 2025-07-15 23:59:59
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59
        
        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);
        
        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("100.00");
        BigDecimal discountedPrice = new BigDecimal("90.00");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);
        
        System.out.println("测试数据:");
        System.out.println("- 订阅开始时间: 2025-07-15 00:00:00");
        System.out.println("- 订阅结束时间: 2025-07-15 23:59:59");
        System.out.println("- 说明: 同一天使用，按1天计算");
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证结果
        // 实际使用天数：同一天按1天计算
        // 周期总天数：7月1日到7月31日 = 31天
        // 比例：1/31 ≈ 0.0323
        BigDecimal expectedProportion = BigDecimal.valueOf(1).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);
        
        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 1天");
        System.out.println("- 周期总天数: 31天");
        System.out.println("- 预期比例: " + expectedProportion);
        
        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);
        
        System.out.println("✅ 同一天使用测试通过");
    }

    /**
     * 测试按比例计算 - 跨月场景
     */
    @Test
    void testCalculateProportion_CrossMonth() {
        System.out.println("=== 测试按比例计算 - 跨月场景 ===");
        
        // 设置测试数据 - 7月25日订阅，8月10日结束，但周期是7月1日到7月31日
        long startTime = 1753459200000L;      // 2025-07-25 00:00:00
        long endTime = 1754928000000L;        // 2025-08-10 00:00:00
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59
        
        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);
        
        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("100.00");
        BigDecimal discountedPrice = new BigDecimal("90.00");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);
        
        System.out.println("测试数据:");
        System.out.println("- 订阅开始时间: 2025-07-25 00:00:00");
        System.out.println("- 订阅结束时间: 2025-08-10 00:00:00");
        System.out.println("- 周期开始时间: 2025-07-01 00:00:00");
        System.out.println("- 周期结束时间: 2025-07-31 23:59:59");
        System.out.println("- 说明: 实际使用时间被限制在周期范围内");
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证结果
        // 实际使用时间：max(7月25日, 7月1日) 到 min(8月10日, 7月31日) = 7月25日到7月31日 = 7天
        // 周期总天数：7月1日到7月31日 = 31天
        // 比例：7/31 ≈ 0.2258
        BigDecimal expectedProportion = BigDecimal.valueOf(7).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);
        
        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 7天 (7月25日到7月31日)");
        System.out.println("- 周期总天数: 31天");
        System.out.println("- 预期比例: " + expectedProportion);
        
        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);
        
        System.out.println("✅ 跨月场景测试通过");
    }

    /**
     * 测试按比例计算 - 边界情况：周期为同一天
     */
    @Test
    void testCalculateProportion_SameDayCycle() {
        System.out.println("=== 测试按比例计算 - 边界情况：周期为同一天 ===");
        
        // 设置测试数据 - 周期和使用都是同一天
        long startTime = 1752595200000L;      // 2025-07-15 00:00:00
        long endTime = 1752681599999L;        // 2025-07-15 23:59:59
        long cycleStartTime = 1752595200000L; // 2025-07-15 00:00:00
        long cycleEndTime = 1752681599999L;   // 2025-07-15 23:59:59
        
        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);
        
        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("100.00");
        BigDecimal discountedPrice = new BigDecimal("90.00");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);
        
        System.out.println("测试数据:");
        System.out.println("- 订阅时间: 2025-07-15 全天");
        System.out.println("- 周期时间: 2025-07-15 全天");
        System.out.println("- 说明: 周期和使用都是同一天，比例应该是1.0");
        
        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);
        
        // 验证结果
        // 实际使用天数：1天
        // 周期总天数：1天
        // 比例：1/1 = 1.0
        BigDecimal expectedProportion = BigDecimal.valueOf(1).divide(BigDecimal.valueOf(1), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);
        
        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 1天");
        System.out.println("- 周期总天数: 1天");
        System.out.println("- 预期比例: " + expectedProportion + " (1.0000)");
        
        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);
        
        System.out.println("✅ 同一天周期测试通过");
    }

    /**
     * 测试按比例计算 - 精度测试
     */
    @Test
    void testCalculateProportion_PrecisionTest() {
        System.out.println("=== 测试按比例计算 - 精度测试 ===");

        // 设置测试数据 - 使用会产生循环小数的天数
        long startTime = 1752595200000L;      // 2025-07-15 00:00:00
        long endTime = 1752854399999L;        // 2025-07-17 23:59:59 (3天)
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59 (31天)

        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);

        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("99.99");
        BigDecimal discountedPrice = new BigDecimal("89.99");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);

        System.out.println("测试数据:");
        System.out.println("- 订阅开始时间: 2025-07-15 00:00:00");
        System.out.println("- 订阅结束时间: 2025-07-17 23:59:59");
        System.out.println("- 原始价格: " + originalPrice);
        System.out.println("- 优惠价格: " + discountedPrice);
        System.out.println("- 说明: 测试精度计算 3/31 的循环小数");

        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);

        // 验证结果
        // 实际使用天数：3天
        // 周期总天数：31天
        // 比例：3/31 ≈ 0.0968 (保留4位小数)
        BigDecimal expectedProportion = BigDecimal.valueOf(3).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);

        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 3天");
        System.out.println("- 周期总天数: 31天");
        System.out.println("- 预期比例: " + expectedProportion);
        System.out.println("- 预期原始价格: " + expectedOriginalPrice);
        System.out.println("- 预期优惠价格: " + expectedDiscountedPrice);

        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);

        // 验证精度
        assertEquals(4, expectedProportion.scale(), "比例应该保留4位小数");

        System.out.println("✅ 精度测试通过");
    }

    /**
     * 测试按比例计算 - 完整月份使用
     */
    @Test
    void testCalculateProportion_FullMonthUsage() {
        System.out.println("=== 测试按比例计算 - 完整月份使用 ===");

        // 设置测试数据 - 完整使用整个周期
        long startTime = 1751299200000L;      // 2025-07-01 00:00:00
        long endTime = 1753891199999L;        // 2025-07-31 23:59:59
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59

        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);

        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("100.00");
        BigDecimal discountedPrice = new BigDecimal("90.00");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);

        System.out.println("测试数据:");
        System.out.println("- 订阅时间: 2025-07-01 到 2025-07-31 (完整月份)");
        System.out.println("- 周期时间: 2025-07-01 到 2025-07-31 (完整月份)");
        System.out.println("- 说明: 完整使用整个周期，比例应该是1.0");

        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);

        // 验证结果
        // 实际使用天数：31天
        // 周期总天数：31天
        // 比例：31/31 = 1.0
        BigDecimal expectedProportion = BigDecimal.valueOf(31).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);

        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 31天");
        System.out.println("- 周期总天数: 31天");
        System.out.println("- 预期比例: " + expectedProportion + " (1.0000)");
        System.out.println("- 预期原始价格: " + expectedOriginalPrice + " (应该等于原价)");
        System.out.println("- 预期优惠价格: " + expectedDiscountedPrice + " (应该等于原优惠价)");

        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);

        // 验证价格没有变化
        assertEquals(originalPrice, expectedOriginalPrice, "完整使用时原价不应该变化");
        assertEquals(discountedPrice, expectedDiscountedPrice, "完整使用时优惠价不应该变化");

        System.out.println("✅ 完整月份使用测试通过");
    }

    /**
     * 测试按比例计算 - 零价格场景
     */
    @Test
    void testCalculateProportion_ZeroPriceScenario() {
        System.out.println("=== 测试按比例计算 - 零价格场景 ===");

        // 设置测试数据
        long startTime = 1752595200000L;      // 2025-07-15 00:00:00
        long endTime = 1754044799999L;        // 2025-07-31 23:59:59
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59

        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);

        // Mock response 初始价格为0
        BigDecimal originalPrice = BigDecimal.ZERO;
        BigDecimal discountedPrice = BigDecimal.ZERO;
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);

        System.out.println("测试数据:");
        System.out.println("- 原始价格: " + originalPrice);
        System.out.println("- 优惠价格: " + discountedPrice);
        System.out.println("- 说明: 测试零价格场景");

        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);

        // 验证结果
        BigDecimal expectedProportion = BigDecimal.valueOf(17).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);

        System.out.println("计算结果:");
        System.out.println("- 预期比例: " + expectedProportion);
        System.out.println("- 预期原始价格: " + expectedOriginalPrice + " (应该仍为0)");
        System.out.println("- 预期优惠价格: " + expectedDiscountedPrice + " (应该仍为0)");

        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);

        // 验证价格仍为0
        assertEquals(BigDecimal.ZERO, expectedOriginalPrice, "零价格乘以任何比例仍应为0");
        assertEquals(BigDecimal.ZERO, expectedDiscountedPrice, "零价格乘以任何比例仍应为0");

        System.out.println("✅ 零价格场景测试通过");
    }

    /**
     * 测试按比例计算 - 月初到月末场景
     */
    @Test
    void testCalculateProportion_MonthStartToEnd() {
        System.out.println("=== 测试按比例计算 - 月初到月末场景 ===");

        // 设置测试数据 - 从月初第一天到月末最后一天
        long startTime = 1751299200000L;      // 2025-07-01 00:00:00
        long endTime = 1753804800000L;        // 2025-07-30 00:00:00 (不包含7月31日)
        long cycleStartTime = 1751299200000L; // 2025-07-01 00:00:00
        long cycleEndTime = 1753891199999L;   // 2025-07-31 23:59:59

        // Mock request
        when(mockRequest.isByProportion()).thenReturn(true);
        when(mockRequest.getStartTime()).thenReturn(startTime);
        when(mockRequest.getEndTime()).thenReturn(endTime);
        when(mockRequest.getCycleStartTime()).thenReturn(cycleStartTime);
        when(mockRequest.getCycleEndTime()).thenReturn(cycleEndTime);

        // Mock response 初始价格
        BigDecimal originalPrice = new BigDecimal("100.00");
        BigDecimal discountedPrice = new BigDecimal("90.00");
        when(mockResponse.getOriginalPrice()).thenReturn(originalPrice);
        when(mockResponse.getDiscountedPrice()).thenReturn(discountedPrice);

        System.out.println("测试数据:");
        System.out.println("- 订阅开始时间: 2025-07-01 00:00:00");
        System.out.println("- 订阅结束时间: 2025-07-30 00:00:00");
        System.out.println("- 周期开始时间: 2025-07-01 00:00:00");
        System.out.println("- 周期结束时间: 2025-07-31 23:59:59");
        System.out.println("- 说明: 使用29天，周期31天");

        // 执行测试
        strategy.testCalculateProportion(mockRequest, mockResponse);

        // 验证结果
        // 实际使用天数：7月1日到7月30日 = 29天
        // 周期总天数：7月1日到7月31日 = 31天
        // 比例：29/31 ≈ 0.9355
        BigDecimal expectedProportion = BigDecimal.valueOf(29).divide(BigDecimal.valueOf(31), 4, RoundingMode.HALF_UP);
        BigDecimal expectedOriginalPrice = originalPrice.multiply(expectedProportion);
        BigDecimal expectedDiscountedPrice = discountedPrice.multiply(expectedProportion);

        System.out.println("计算结果:");
        System.out.println("- 实际使用天数: 29天");
        System.out.println("- 周期总天数: 31天");
        System.out.println("- 预期比例: " + expectedProportion);
        System.out.println("- 预期原始价格: " + expectedOriginalPrice);
        System.out.println("- 预期优惠价格: " + expectedDiscountedPrice);

        // 验证方法调用
        verify(mockResponse).setOriginalPrice(expectedOriginalPrice);
        verify(mockResponse).setDiscountedPrice(expectedDiscountedPrice);

        System.out.println("✅ 月初到月末场景测试通过");
    }
}
