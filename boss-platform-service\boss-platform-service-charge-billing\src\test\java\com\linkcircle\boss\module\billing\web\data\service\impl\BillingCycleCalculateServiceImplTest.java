package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.billing.web.data.service.BillingCycleCalculateService;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.enums.BillingCycleTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @date 2025-07-08 17:30
 * @description 出账周期计算服务测试
 */
class BillingCycleCalculateServiceImplTest {

    private BillingCycleCalculateService billingCycleCalculateService;
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @BeforeEach
    void setUp() {
        billingCycleCalculateService = new BillingCycleCalculateServiceImpl();
        System.out.println("=== 出账周期计算服务测试开始 ===");
    }

    @Test
    void testCalculateBillingCycle_NullAccountInfo() {
        System.out.println("\n--- 测试空账户信息 ---");

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(null);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        assertNotNull(result);
        assertFalse(result.getAllowBilling());
        assertEquals("账户信息为空", result.getErrorMessage());
    }

    @Test
    void testCalculateBillingCycle_InvalidBillingCycleType() {
        System.out.println("\n--- 测试无效的出账周期类型 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(999); // 无效类型
        accountInfo.setBillingDay(15);
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        assertNotNull(result);
        assertFalse(result.getAllowBilling());
        assertTrue(result.getErrorMessage().contains("不支持的出账周期类型"));
    }

    @Test
    void testCalculateBillingCycle_MonthlyBilling_InvalidDay() {
        System.out.println("\n--- 测试月结出账无效日期 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(35); // 无效日期
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        assertNotNull(result);
        assertFalse(result.getAllowBilling());
        assertTrue(result.getErrorMessage().contains("月结出账日配置无效"));
    }

    @Test
    void testCalculateBillingCycle_WeeklyBilling_InvalidDay() {
        System.out.println("\n--- 测试周结出账无效星期 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.WEEKLY.getType());
        accountInfo.setBillingDay(8); // 无效星期
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        assertNotNull(result);
        assertFalse(result.getAllowBilling());
        assertTrue(result.getErrorMessage().contains("周结出账星期配置无效"));
    }

    @Test
    void testCalculateBillingCycle_QuarterlyBilling_InvalidDay() {
        System.out.println("\n--- 测试季结出账无效日期 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.QUARTERLY.getType());
        accountInfo.setBillingDay(100); // 无效日期
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        assertNotNull(result);
        assertFalse(result.getAllowBilling());
        assertTrue(result.getErrorMessage().contains("季结出账日配置无效"));
    }

    @Test
    void testCalculateBillingCycle_YearlyBilling_InvalidDay() {
        System.out.println("\n--- 测试年结出账无效日期 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.YEARLY.getType());
        accountInfo.setBillingDay(400); // 无效日期
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        assertNotNull(result);
        assertFalse(result.getAllowBilling());
        assertTrue(result.getErrorMessage().contains("年结出账日配置无效"));
    }

    @Test
    void testCalculateBillingCycle_MonthlyBilling_NotBillingDay() {
        System.out.println("\n--- 测试月结出账非出账日 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(1); // 每月1号出账
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        if (result.getAllowBilling()) {
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());
        }

        assertNotNull(result);
        // 结果取决于当前是否为1号
    }

    @Test
    void testCalculateBillingCycle_WeeklyBilling_NotBillingDay() {
        System.out.println("\n--- 测试周结出账非出账星期 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.WEEKLY.getType());
        accountInfo.setBillingDay(1); // 每周一出账
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());
        System.out.println("错误信息: " + result.getErrorMessage());

        if (result.getAllowBilling()) {
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());
        }

        assertNotNull(result);
        // 结果取决于当前是否为周一
    }

    @Test
    void testCalculateBillingCycle_AllowedBilling() {
        System.out.println("\n--- 测试允许出账的情况 ---");

        // 今天是7月8日（周二），测试周二出账的情况
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1001L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.WEEKLY.getType());
        accountInfo.setBillingDay(2); // 周二出账
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("测试周二出账场景:");
        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay() + " (周二)");

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());

        if (result.getAllowBilling()) {
            System.out.println("✅ 允许出账!");
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期: " + result.getBillingCycle());
            System.out.println("出账周期类型: " + result.getBillingCycleType());
            System.out.println("出账日: " + result.getBillingDay());
            System.out.println("时区: " + result.getTimezone());
            System.out.println("当前时间: " + formatTimestamp(result.getCurrentTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());

            // 验证时间范围合理性
            assertTrue(result.getBillingStartTime() < result.getBillingEndTime(), "开始时间应该小于结束时间");
            assertTrue(result.getBillingEndTime() < result.getCurrentTime(), "结束时间应该小于当前时间");
        } else {
            System.out.println("❌ 不允许出账: " + result.getErrorMessage());
        }

        assertNotNull(result);

        // 测试今天是8号的月结出账
        System.out.println("\n--- 测试8号月结出账 ---");
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(8); // 每月8号出账

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay() + " (每月8号)");

        result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());

        if (result.getAllowBilling()) {
            System.out.println("✅ 允许出账!");
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());
        } else {
            System.out.println("❌ 不允许出账: " + result.getErrorMessage());
        }

        // 测试季度出账 - 今天是2025年7月8日，是第3季度的第8天（7月1日是第3季度第1天）
        System.out.println("\n--- 测试季度第8天出账 ---");
        accountInfo.setBillingCycle(BillingCycleTypeEnum.QUARTERLY.getType());
        accountInfo.setBillingDay(8); // 每季度第8天出账（7月8日是第3季度第8天）

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay() + " (每季度第8天)");

        result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());

        if (result.getAllowBilling()) {
            System.out.println("✅ 允许出账!");
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());

            // 验证时间范围合理性
            assertTrue(result.getBillingStartTime() < result.getBillingEndTime(), "开始时间应该小于结束时间");
            assertTrue(result.getBillingEndTime() < result.getCurrentTime(), "结束时间应该小于当前时间");
        } else {
            System.out.println("❌ 不允许出账: " + result.getErrorMessage());
        }

        // 测试年度出账 - 今天是2025年7月8日，是一年中的第189天
        System.out.println("\n--- 测试年度第189天出账 ---");
        accountInfo.setBillingCycle(BillingCycleTypeEnum.YEARLY.getType());
        accountInfo.setBillingDay(189); // 每年第189天出账（7月8日是一年中第189天）

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay() + " (每年第189天，即7月8日)");

        result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());

        if (result.getAllowBilling()) {
            System.out.println("✅ 允许出账!");
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());

            // 验证时间范围合理性
            assertTrue(result.getBillingStartTime() < result.getBillingEndTime(), "开始时间应该小于结束时间");
            assertTrue(result.getBillingEndTime() < result.getCurrentTime(), "结束时间应该小于当前时间");
        } else {
            System.out.println("❌ 不允许出账: " + result.getErrorMessage());
        }

        System.out.println("\n=== 允许出账测试完成 ===");
    }

    @Test
    void testCalculateBillingCycle_AllCycleTypes() {
        System.out.println("\n--- 测试所有出账周期类型 ---");

        // 测试月结
        testCycleType(BillingCycleTypeEnum.MONTHLY, 15, "月结");

        // 测试周结
        testCycleType(BillingCycleTypeEnum.WEEKLY, 1, "周结");

        // 测试季结
        testCycleType(BillingCycleTypeEnum.QUARTERLY, 30, "季结");

        // 测试年结
        testCycleType(BillingCycleTypeEnum.YEARLY, 100, "年结");
    }

    private void testCycleType(BillingCycleTypeEnum cycleType, Integer billingDay, String typeName) {
        System.out.println("\n--- " + typeName + "出账测试 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(cycleType.getType());
        accountInfo.setBillingDay(billingDay);
        accountInfo.setTimezone("Asia/Shanghai");

        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("测试结果: " + result);
        System.out.println("允许出账: " + result.getAllowBilling());

        if (result.getAllowBilling()) {
            System.out.println("出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("出账结束时间: " + formatTimestamp(result.getBillingEndTime()));
            System.out.println("出账周期描述: " + result.getBillingPeriodDescription());
        } else {
            System.out.println("错误信息: " + result.getErrorMessage());
        }

        assertNotNull(result);
    }

    @Test
    void testCalculateBillingCycle_EdgeCases() {
        System.out.println("\n--- 测试边界情况 ---");

        // 测试空参数
        testNullParameters();

        // 测试边界值
        testBoundaryValues();

        // 测试时区处理
        testTimezoneHandling();
    }

    private void testNullParameters() {
        System.out.println("\n--- 测试空参数情况 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        // 不设置billingCycle
        accountInfo.setBillingDay(15);
        accountInfo.setTimezone("Asia/Shanghai");

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        System.out.println("空billingCycle结果: " + result.getErrorMessage());
        assertFalse(result.getAllowBilling());

        // 不设置billingDay
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(null);

        result = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        System.out.println("空billingDay结果: " + result.getErrorMessage());
        assertFalse(result.getAllowBilling());
    }

    private void testBoundaryValues() {
        System.out.println("\n--- 测试边界值 ---");

        // 月结边界值
        testMonthlyBoundary();

        // 周结边界值
        testWeeklyBoundary();

        // 季结边界值
        testQuarterlyBoundary();

        // 年结边界值
        testYearlyBoundary();
    }

    private void testMonthlyBoundary() {
        System.out.println("月结边界值测试:");

        // 测试最小值
        testBoundaryValue(BillingCycleTypeEnum.MONTHLY, 1, "月结最小值");

        // 测试最大值
        testBoundaryValue(BillingCycleTypeEnum.MONTHLY, 28, "月结最大值");

        // 测试超出范围
        testBoundaryValue(BillingCycleTypeEnum.MONTHLY, 0, "月结小于最小值");
        testBoundaryValue(BillingCycleTypeEnum.MONTHLY, 29, "月结大于最大值");
    }

    private void testWeeklyBoundary() {
        System.out.println("周结边界值测试:");

        // 测试最小值
        testBoundaryValue(BillingCycleTypeEnum.WEEKLY, 1, "周结最小值");

        // 测试最大值
        testBoundaryValue(BillingCycleTypeEnum.WEEKLY, 7, "周结最大值");

        // 测试超出范围
        testBoundaryValue(BillingCycleTypeEnum.WEEKLY, 0, "周结小于最小值");
        testBoundaryValue(BillingCycleTypeEnum.WEEKLY, 8, "周结大于最大值");
    }

    private void testQuarterlyBoundary() {
        System.out.println("季结边界值测试:");

        // 测试最小值
        testBoundaryValue(BillingCycleTypeEnum.QUARTERLY, 1, "季结最小值");

        // 测试最大值
        testBoundaryValue(BillingCycleTypeEnum.QUARTERLY, 90, "季结最大值");

        // 测试超出范围
        testBoundaryValue(BillingCycleTypeEnum.QUARTERLY, 0, "季结小于最小值");
        testBoundaryValue(BillingCycleTypeEnum.QUARTERLY, 91, "季结大于最大值");
    }

    private void testYearlyBoundary() {
        System.out.println("年结边界值测试:");

        // 测试最小值
        testBoundaryValue(BillingCycleTypeEnum.YEARLY, 1, "年结最小值");

        // 测试最大值
        testBoundaryValue(BillingCycleTypeEnum.YEARLY, 366, "年结最大值");

        // 测试超出范围
        testBoundaryValue(BillingCycleTypeEnum.YEARLY, 0, "年结小于最小值");
        testBoundaryValue(BillingCycleTypeEnum.YEARLY, 367, "年结大于最大值");
    }

    private void testBoundaryValue(BillingCycleTypeEnum cycleType, Integer billingDay, String description) {
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(cycleType.getType());
        accountInfo.setBillingDay(billingDay);
        accountInfo.setTimezone("Asia/Shanghai");

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        System.out.println(description + " - 允许出账: " + result.getAllowBilling() +
                ", 错误信息: " + result.getErrorMessage());
    }

    private void testTimezoneHandling() {
        System.out.println("\n--- 测试时区处理 ---");

        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(1L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(15);

        // 测试空时区
        accountInfo.setTimezone(null);
        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        System.out.println("空时区处理结果: " + (result.getAllowBilling() ? "成功" : result.getErrorMessage()));

        // 测试空字符串时区
        accountInfo.setTimezone("");
        result = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        System.out.println("空字符串时区处理结果: " + (result.getAllowBilling() ? "成功" : result.getErrorMessage()));

        // 测试无效时区
        accountInfo.setTimezone("Invalid/Timezone");
        result = billingCycleCalculateService.calculateBillingCycle(accountInfo);
        System.out.println("无效时区处理结果: " + (result.getAllowBilling() ? "成功" : result.getErrorMessage()));
    }

    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "null";
        }
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of("Asia/Shanghai"));
        return dateTime.format(FORMATTER);
    }

    /**
     * 测试月结出账 - 自然月计算
     * 验证修改后的月结出账是否按自然月计算
     */
    @Test
    void testCalculateMonthlyBilling_NaturalMonth() {
        System.out.println("\n=== 测试月结出账 - 自然月计算 ===");

        // 构造账户信息：每月1号出账
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(12345L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(1);
        accountInfo.setTimezone("Asia/Shanghai");
        accountInfo.setCurrency("CNY");

        System.out.println("测试场景：每月1号出账，应该出账上个自然月（1号到月末）的账单");
        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        // 调用计算方法
        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        // 验证结果
        System.out.println("计算结果:");
        System.out.println("- 账户ID: " + result.getAccountId());
        System.out.println("- 是否允许出账: " + result.getAllowBilling());
        System.out.println("- 出账周期: " + result.getBillingCycle());
        System.out.println("- 出账描述: " + result.getBillingPeriodDescription());

        if (result.getAllowBilling()) {
            System.out.println("- 出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("- 出账结束时间: " + formatTimestamp(result.getBillingEndTime()));

            // 验证是否为自然月计算
            // 如果今天是1号，应该出账上个自然月的账单
            assertTrue(result.getBillingPeriodDescription().contains("自然月"), "出账描述应该包含'自然月'");

            // 验证时间范围合理性
            assertTrue(result.getBillingStartTime() < result.getBillingEndTime(), "开始时间应该小于结束时间");
            assertTrue(result.getBillingEndTime() < result.getCurrentTime(), "结束时间应该小于当前时间");
        } else {
            System.out.println("- 错误信息: " + result.getErrorMessage());
            // 如果今天不是1号，应该返回不允许出账
            assertTrue(result.getErrorMessage().contains("当前不是出账日"), "错误信息应该说明当前不是出账日");
        }

        assertNotNull(result);
    }

    /**
     * 测试年结出账 - 自然年计算
     * 验证修改后的年结出账是否按自然年计算
     */
    @Test
    void testCalculateYearlyBilling_NaturalYear() {
        System.out.println("\n=== 测试年结出账 - 自然年计算 ===");

        // 构造账户信息：每年第1天（1月1日）出账
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(12345L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.YEARLY.getType());
        accountInfo.setBillingDay(1); // 第1天表示1月1日
        accountInfo.setTimezone("Asia/Shanghai");
        accountInfo.setCurrency("CNY");

        System.out.println("测试场景：每年1月1日出账，应该出账上个自然年（1月1日到12月31日）的账单");
        System.out.println("账户信息: accountId=" + accountInfo.getAccountId() +
                ", billingCycle=" + accountInfo.getBillingCycle() +
                ", billingDay=" + accountInfo.getBillingDay());

        // 调用计算方法
        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        // 验证结果
        System.out.println("计算结果:");
        System.out.println("- 账户ID: " + result.getAccountId());
        System.out.println("- 是否允许出账: " + result.getAllowBilling());
        System.out.println("- 出账周期: " + result.getBillingCycle());
        System.out.println("- 出账描述: " + result.getBillingPeriodDescription());

        if (result.getAllowBilling()) {
            System.out.println("- 出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("- 出账结束时间: " + formatTimestamp(result.getBillingEndTime()));

            // 验证是否为自然年计算
            // 如果今天是1月1日，应该出账上个自然年的账单
            assertTrue(result.getBillingPeriodDescription().contains("自然年"), "出账描述应该包含'自然年'");

            // 验证时间范围合理性
            assertTrue(result.getBillingStartTime() < result.getBillingEndTime(), "开始时间应该小于结束时间");
            assertTrue(result.getBillingEndTime() < result.getCurrentTime(), "结束时间应该小于当前时间");

            // 验证出账周期格式（应该是上一年的1月1日到12月31日）
            String billingCycle = result.getBillingCycle();
            assertTrue(billingCycle.contains("-"), "出账周期应该包含'-'分隔符");
            String[] dates = billingCycle.split("-");
            assertEquals(2, dates.length, "出账周期应该包含开始和结束日期");

            // 验证开始日期是上一年的1月1日
            assertTrue(dates[0].endsWith("0101"), "开始日期应该是上一年的1月1日");
            // 验证结束日期是上一年的12月31日
            assertTrue(dates[1].endsWith("1231"), "结束日期应该是上一年的12月31日");
        } else {
            System.out.println("- 错误信息: " + result.getErrorMessage());
            // 如果今天不是1月1日，应该返回不允许出账
            assertTrue(result.getErrorMessage().contains("当前不是出账日"), "错误信息应该说明当前不是出账日");
        }

        assertNotNull(result);
    }

    /**
     * 测试月结出账 - 不同月份的自然月计算
     * 模拟不同月份的情况来验证自然月计算的正确性
     */
    @Test
    void testCalculateMonthlyBilling_DifferentMonths() {
        System.out.println("\n=== 测试月结出账 - 不同月份的自然月计算 ===");

        // 测试每月15号出账的情况
        CustomerAccountVO accountInfo = new CustomerAccountVO();
        accountInfo.setAccountId(12345L);
        accountInfo.setBillingCycle(BillingCycleTypeEnum.MONTHLY.getType());
        accountInfo.setBillingDay(15);
        accountInfo.setTimezone("Asia/Shanghai");
        accountInfo.setCurrency("CNY");

        System.out.println("测试场景：每月15号出账");
        System.out.println("说明：如果今天是15号，应该出账上个自然月（1号到月末）的账单");

        BillingCycleResultVO result = billingCycleCalculateService.calculateBillingCycle(accountInfo);

        System.out.println("计算结果:");
        System.out.println("- 是否允许出账: " + result.getAllowBilling());
        System.out.println("- 出账描述: " + result.getBillingPeriodDescription());

        if (result.getAllowBilling()) {
            System.out.println("- 出账开始时间: " + formatTimestamp(result.getBillingStartTime()));
            System.out.println("- 出账结束时间: " + formatTimestamp(result.getBillingEndTime()));

            // 验证是否为自然月计算
            assertTrue(result.getBillingPeriodDescription().contains("自然月"), "出账描述应该包含'自然月'");
        } else {
            System.out.println("- 错误信息: " + result.getErrorMessage());
        }

        assertNotNull(result);
    }
}
