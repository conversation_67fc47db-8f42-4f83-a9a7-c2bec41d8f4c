package com.linkcircle.boss.module.billing.web.data.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;

/**
 * <AUTHOR>
 * @date 2025-06-20 17:22
 * @description 周期服务 时间间隔计算
 */
public interface CyclePeriodCalculateService {

    /**
     * 计算周期信息
     *
     * @param periodUnit   周期单位
     * @param timezone     时区
     * @param businessTime 业务时间
     * @param startTime    开始时间
     * @param period       周期
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, long businessTime, long startTime, int period);

    /**
     * 计算基于周的间隔时长的当前周期信息
     *
     * @param timezone      时区
     * @param businessTime  业务时间（毫秒时间戳）
     * @param startTime     开始时间（毫秒时间戳）
     * @param intervalWeeks 间隔时长（周数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, long startTime, int intervalWeeks);

    /**
     * 计算基于天的间隔时长的当前周期信息
     *
     * @param timezone     时区
     * @param businessTime 业务时间（毫秒时间戳）
     * @param startTime    开始时间（毫秒时间戳）
     * @param intervalDays 间隔时长（天数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, long startTime, int intervalDays);

    /**
     * 计算基于月的间隔时长的当前周期信息
     *
     * @param timezone       时区
     * @param businessTime   业务时间（毫秒时间戳）
     * @param startTime      开始时间（毫秒时间戳）
     * @param intervalMonths 间隔时长（月数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, long startTime, int intervalMonths);

    /**
     * 计算基于季度的间隔时长的当前周期信息
     *
     * @param timezone         时区
     * @param businessTime     业务时间（毫秒时间戳）
     * @param startTime        开始时间（毫秒时间戳）
     * @param intervalQuarters 间隔时长（季度数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, long startTime, int intervalQuarters);

    /**
     * 计算基于年的间隔时长的当前周期信息
     *
     * @param timezone      时区
     * @param businessTime  业务时间（毫秒时间戳）
     * @param startTime     开始时间（毫秒时间戳）
     * @param intervalYears 间隔时长（年数）
     * @return 当前周期信息，包含开始和结束时间戳
     */
    CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, long startTime, int intervalYears);
}
