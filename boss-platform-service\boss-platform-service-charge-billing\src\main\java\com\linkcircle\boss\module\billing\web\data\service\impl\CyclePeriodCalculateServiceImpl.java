package com.linkcircle.boss.module.billing.web.data.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.crm.enums.PeriodUnitEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2025-06-20 17:24
 * @description 周期服务实现类 时间间隔计算
 */
@Slf4j
@Service
public class CyclePeriodCalculateServiceImpl implements CyclePeriodCalculateService {

    @Override
    public CyclePeriodResultVO calculateCyclePeriod(Integer periodUnit, String timezone, long businessTime, long startTime, int period) {
        PeriodUnitEnum periodUnitEnum = PeriodUnitEnum.getByUnit(periodUnit);
        CyclePeriodResultVO cyclePeriodResultVO = switch (periodUnitEnum) {
            case YEAR -> calculateYearCyclePeriod(timezone, businessTime, startTime, period);
            case QUARTER -> calculateQuarterCyclePeriod(timezone, businessTime, startTime, period);
            case MONTH -> calculateMonthCyclePeriod(timezone, businessTime, startTime, period);
            case WEEK -> calculateWeekCyclePeriod(timezone, businessTime, startTime, period);
            case DAY -> calculateDayCyclePeriod(timezone, businessTime, startTime, period);
            case ONCE ->
                // 一次 使用开始时间
                    CyclePeriodResultVO.success(1L, startTime, startTime, businessTime);
        };
        cyclePeriodResultVO.setPeriodUnitEnum(periodUnitEnum);
        cyclePeriodResultVO.setPeriod(period);
        if (Objects.nonNull(cyclePeriodResultVO.getCycleStartTime()) && Objects.nonNull(cyclePeriodResultVO.getCycleEndTime())) {
            // 转成yyyyMMdd
            TimeZone tz = TimeZone.getTimeZone(timezone);
            String start = DateUtil.format(new DateTime(cyclePeriodResultVO.getCycleStartTime()).setTimeZone(tz), "yyyyMMdd");
            String end = DateUtil.format(new DateTime(cyclePeriodResultVO.getCycleEndTime()).setTimeZone(tz), "yyyyMMdd");
            cyclePeriodResultVO.setBillingCycle(start + "-" + end);
            cyclePeriodResultVO.setTimezone(timezone);
        }
        return cyclePeriodResultVO;
    }

    @Override
    public CyclePeriodResultVO calculateWeekCyclePeriod(String timezone, long businessTime, long startTime, int intervalWeeks) {
        if (intervalWeeks <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然周周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔周数: {}",
                    timezone, businessDatetime, startDatetime, intervalWeeks);

            // 检查业务时间是否已经到达开始时间
            if (businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将开始时间调整到所在周的周一开始（自然周：周一到周日）
            DateTime startWeekBegin = DateUtil.beginOfWeek(startDatetime, true);

            // 将业务时间调整到所在周的周一开始
            DateTime businessWeekBegin = DateUtil.beginOfWeek(businessDatetime, true);

            log.debug("开始时间所在周的周一: {}, 业务时间所在周的周一: {}", startWeekBegin, businessWeekBegin);

            // 使用毫秒差计算周数差，避免跨年问题
            long millisDiff = businessWeekBegin.getTime() - startWeekBegin.getTime();
            long weeksDiff = millisDiff / (7 * 24 * 60 * 60 * 1000L);

            log.debug("毫秒差: {}, 周数差: {}", millisDiff, weeksDiff);

            // 计算是第几个周期（从0开始）
            long cycleIndex = weeksDiff / intervalWeeks;

            // 计算当前周期的起始时间（使用毫秒偏移）
            long cycleStartMillis = startWeekBegin.getTime() + (cycleIndex * intervalWeeks * 7 * 24 * 60 * 60 * 1000L);

            // 计算当前周期的结束时间（周期最后一天的23:59:59.999）
            long cycleEndMillis = cycleStartMillis + (intervalWeeks * 7 * 24 * 60 * 60 * 1000L) - 1;

            log.debug("周期索引: {}, 周期开始时间戳: {}, 周期结束时间戳: {}", cycleIndex, cycleStartMillis, cycleEndMillis);

            return CyclePeriodResultVO.success(cycleIndex, cycleStartMillis, cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然周周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然周周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateDayCyclePeriod(String timezone, long businessTime, long startTime, int intervalDays) {
        if (intervalDays <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然日周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔天数: {}",
                    timezone, businessDatetime, startDatetime, intervalDays);

            // 检查业务时间是否已经到达开始时间
            if (businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将时间调整到当天开始
            DateTime startDayBegin = DateUtil.beginOfDay(startDatetime);
            DateTime businessDayBegin = DateUtil.beginOfDay(businessDatetime);

            log.debug("开始时间当天开始: {}, 业务时间当天开始: {}", startDayBegin, businessDayBegin);

            // 使用毫秒差计算天数差
            long millisDiff = businessDayBegin.getTime() - startDayBegin.getTime();
            long daysDiff = millisDiff / (24 * 60 * 60 * 1000L);

            log.debug("毫秒差: {}, 天数差: {}", millisDiff, daysDiff);

            // 计算是第几个周期（从0开始）
            long cycleIndex = daysDiff / intervalDays;

            // 计算当前周期的起始时间（使用毫秒偏移）
            long cycleStartMillis = startDayBegin.getTime() + (cycleIndex * intervalDays * 24 * 60 * 60 * 1000L);

            // 计算当前周期的结束时间（当天的23:59:59.999）
            long cycleEndMillis = cycleStartMillis + (intervalDays * 24 * 60 * 60 * 1000L) - 1;

            log.debug("周期索引: {}, 周期开始时间戳: {}, 周期结束时间戳: {}", cycleIndex, cycleStartMillis, cycleEndMillis);

            return CyclePeriodResultVO.success(cycleIndex, cycleStartMillis, cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然日周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然日周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateMonthCyclePeriod(String timezone, long businessTime, long startTime, int intervalMonths) {
        if (intervalMonths <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然月周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔月数: {}",
                    timezone, businessDatetime, startDatetime, intervalMonths);

            // 检查业务时间是否已经到达开始时间
            if (businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将时间调整到当月开始
            DateTime startMonthBegin = DateUtil.beginOfMonth(startDatetime);
            DateTime businessMonthBegin = DateUtil.beginOfMonth(businessDatetime);

            log.debug("开始时间当月开始: {}, 业务时间当月开始: {}", startMonthBegin, businessMonthBegin);

            // 计算月数差
            int startYear = startMonthBegin.year();
            int startMonth = startMonthBegin.month() + 1; // hutool月份从0开始
            int businessYear = businessMonthBegin.year();
            int businessMonth = businessMonthBegin.month() + 1;

            long monthsDiff = (businessYear - startYear) * 12L + (businessMonth - startMonth);

            log.debug("月数差: {}", monthsDiff);

            // 计算是第几个周期（从0开始）
            long cycleIndex = monthsDiff / intervalMonths;

            // 计算当前周期的起始时间
            DateTime cycleStartTime = DateUtil.offsetMonth(startMonthBegin, (int) (cycleIndex * intervalMonths));

            // 计算当前周期的结束时间（周期最后一天的23:59:59.999）
            DateTime nextCycleStartTime = DateUtil.offsetMonth(cycleStartTime, intervalMonths);
            long cycleEndMillis = nextCycleStartTime.getTime() - 1;

            log.debug("周期索引: {}, 周期开始时间: {}, 周期结束时间: {}", cycleIndex, cycleStartTime, new DateTime(cycleEndMillis));

            return CyclePeriodResultVO.success(cycleIndex, cycleStartTime.getTime(), cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然月周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然月周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateQuarterCyclePeriod(String timezone, long businessTime, long startTime, int intervalQuarters) {
        if (intervalQuarters <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然季度周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔季度数: {}",
                    timezone, businessDatetime, startDatetime, intervalQuarters);

            // 检查业务时间是否已经到达开始时间
            if (businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将开始时间调整到所在季度的第一个月开始
            DateTime startQuarterBegin = getQuarterBegin(startDatetime);

            // 将业务时间调整到所在季度的第一个月开始
            DateTime businessQuarterBegin = getQuarterBegin(businessDatetime);

            log.debug("开始时间所在季度开始: {}, 业务时间所在季度开始: {}", startQuarterBegin, businessQuarterBegin);

            // 计算季度差
            int startYear = startQuarterBegin.year();
            int startQuarter = getQuarter(startQuarterBegin);
            int businessYear = businessQuarterBegin.year();
            int businessQuarter = getQuarter(businessQuarterBegin);

            long quartersDiff = (businessYear - startYear) * 4L + (businessQuarter - startQuarter);

            log.debug("季度差: {}", quartersDiff);

            // 计算是第几个周期（从0开始）
            long cycleIndex = quartersDiff / intervalQuarters;

            // 计算当前周期的起始时间（按季度偏移）
            DateTime cycleStartTime = offsetQuarter(startQuarterBegin, (int) (cycleIndex * intervalQuarters));

            // 计算当前周期的结束时间（周期最后一天的23:59:59.999）
            DateTime nextCycleStartTime = offsetQuarter(cycleStartTime, intervalQuarters);
            long cycleEndMillis = nextCycleStartTime.getTime() - 1;

            log.debug("周期索引: {}, 周期开始时间: {}, 周期结束时间: {}", cycleIndex, cycleStartTime, new DateTime(cycleEndMillis));

            return CyclePeriodResultVO.success(cycleIndex, cycleStartTime.getTime(), cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然季度周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然季度周期信息异常: " + e.getMessage());
        }
    }

    @Override
    public CyclePeriodResultVO calculateYearCyclePeriod(String timezone, long businessTime, long startTime, int intervalYears) {
        if (intervalYears <= 0) {
            return CyclePeriodResultVO.failure("间隔时长必须大于0");
        }

        try {
            TimeZone tz = TimeZone.getTimeZone(timezone);

            // 转换为指定时区的DateTime
            DateTime businessDatetime = DateUtil.date(businessTime).setTimeZone(tz);
            DateTime startDatetime = DateUtil.date(startTime).setTimeZone(tz);

            log.debug("计算自然年周期信息 - 时区: {}, 业务时间: {}, 开始时间: {}, 间隔年数: {}",
                    timezone, businessDatetime, startDatetime, intervalYears);

            // 检查业务时间是否已经到达开始时间
            if (businessDatetime.before(startDatetime)) {
                log.debug("业务时间还未到达开始时间");
                return CyclePeriodResultVO.notStarted();
            }

            // 将时间调整到当年开始（1月1日）
            DateTime startYearBegin = DateUtil.beginOfYear(startDatetime);
            DateTime businessYearBegin = DateUtil.beginOfYear(businessDatetime);

            log.debug("开始时间当年开始: {}, 业务时间当年开始: {}", startYearBegin, businessYearBegin);

            // 计算年数差
            int startYear = startYearBegin.year();
            int businessYear = businessYearBegin.year();
            long yearsDiff = businessYear - startYear;

            log.debug("年数差: {}", yearsDiff);

            // 计算是第几个周期（从0开始）
            long cycleIndex = yearsDiff / intervalYears;

            // 计算当前周期的起始时间
            DateTime cycleStartTime = DateUtil.offsetYear(startYearBegin, (int) (cycleIndex * intervalYears));

            // 计算当前周期的结束时间（周期最后一天的23:59:59.999）
            DateTime nextCycleStartTime = DateUtil.offsetYear(cycleStartTime, intervalYears);
            long cycleEndMillis = nextCycleStartTime.getTime() - 1;

            log.debug("周期索引: {}, 周期开始时间: {}, 周期结束时间: {}", cycleIndex, cycleStartTime, new DateTime(cycleEndMillis));

            return CyclePeriodResultVO.success(cycleIndex, cycleStartTime.getTime(), cycleEndMillis, businessTime);

        } catch (Exception e) {
            log.error("计算自然年周期信息异常", e);
            return CyclePeriodResultVO.failure("计算自然年周期信息异常: " + e.getMessage());
        }
    }

    /**
     * 获取指定时间所在季度的开始时间
     *
     * @param dateTime 指定时间
     * @return 季度开始时间
     */
    private DateTime getQuarterBegin(DateTime dateTime) {
        int month = dateTime.month() + 1; // hutool月份从0开始，转换为1-12
        int quarterStartMonth;

        if (month <= 3) {
            quarterStartMonth = 1; // Q1: 1-3月
        } else if (month <= 6) {
            quarterStartMonth = 4; // Q2: 4-6月
        } else if (month <= 9) {
            quarterStartMonth = 7; // Q3: 7-9月
        } else {
            quarterStartMonth = 10; // Q4: 10-12月
        }

        // 先设置到目标月份，然后获取月初
        DateTime targetMonth = DateUtil.offsetMonth(DateUtil.beginOfYear(dateTime), quarterStartMonth - 1);
        return DateUtil.beginOfMonth(targetMonth);
    }

    /**
     * 获取指定时间所在的季度（1-4）
     *
     * @param dateTime 指定时间
     * @return 季度编号
     */
    private int getQuarter(DateTime dateTime) {
        int month = dateTime.month() + 1; // hutool月份从0开始，转换为1-12
        return (month - 1) / 3 + 1;
    }

    /**
     * 按季度偏移时间
     *
     * @param dateTime 基准时间
     * @param quarters 偏移的季度数
     * @return 偏移后的时间
     */
    private DateTime offsetQuarter(DateTime dateTime, int quarters) {
        // 每个季度3个月
        return DateUtil.offsetMonth(dateTime, quarters * 3);
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        CyclePeriodCalculateServiceImpl service = new CyclePeriodCalculateServiceImpl();
        String timezone = "Asia/Shanghai";

        // 设置测试数据
        // 开始时间：2024年1月15日
        DateTime startDateTime = DateUtil.parse("2024-01-15 10:30:00", "yyyy-MM-dd HH:mm:ss");
        long startTime = startDateTime.getTime();

        // 当前时间：2025年6月20日
        DateTime currentDateTime = DateUtil.parse("2025-06-20 15:45:00", "yyyy-MM-dd HH:mm:ss");
        long currentTime = currentDateTime.getTime();

        System.out.println("=== 周期计算测试 ===");
        System.out.println("开始时间: " + startDateTime);
        System.out.println("当前时间: " + currentDateTime);
        System.out.println();

        // 测试天周期
        testDayCycle(service, timezone, currentTime, startTime);

        // 测试周周期
        // testWeekCycle(service, timezone, currentTime, startTime);

        // 测试月周期
        // testMonthCycle(service, timezone, currentTime, startTime);
        //
        // 测试季度周期
        // testQuarterCycle(service, timezone, currentTime, startTime);

        // 测试年周期
        // testYearCycle(service, timezone, currentTime, startTime);
    }

    private static void testDayCycle(CyclePeriodCalculateServiceImpl service, String timezone, long currentTime, long startTime) {
        System.out.println("=== 天周期测试 ===");

        // 测试7天间隔
        CyclePeriodResultVO result = service.calculateDayCyclePeriod(timezone, currentTime, startTime, 7);
        printResult("7天间隔", result, timezone);

        // 测试30天间隔
        result = service.calculateDayCyclePeriod(timezone, currentTime, startTime, 30);
        printResult("30天间隔", result, timezone);

        System.out.println();
    }

    private static void testWeekCycle(CyclePeriodCalculateServiceImpl service, String timezone, long currentTime, long startTime) {
        System.out.println("=== 周周期测试 ===");

        // 测试2周间隔
        CyclePeriodResultVO result = service.calculateWeekCyclePeriod(timezone, currentTime, startTime, 2);
        printResult("2周间隔", result, timezone);

        // 测试4周间隔
        result = service.calculateWeekCyclePeriod(timezone, currentTime, startTime, 4);
        printResult("4周间隔", result, timezone);

        System.out.println();
    }

    private static void testMonthCycle(CyclePeriodCalculateServiceImpl service, String timezone, long currentTime, long startTime) {
        System.out.println("=== 月周期测试 ===");

        // 测试1月间隔
        CyclePeriodResultVO result = service.calculateMonthCyclePeriod(timezone, currentTime, startTime, 1);
        printResult("1月间隔", result, timezone);

        // 测试3月间隔
        result = service.calculateMonthCyclePeriod(timezone, currentTime, startTime, 3);
        printResult("3月间隔", result, timezone);

        // 测试6月间隔
        result = service.calculateMonthCyclePeriod(timezone, currentTime, startTime, 6);
        printResult("6月间隔", result, timezone);

        System.out.println();
    }

    private static void testQuarterCycle(CyclePeriodCalculateServiceImpl service, String timezone, long currentTime, long startTime) {
        System.out.println("=== 季度周期测试 ===");

        // 测试1季度间隔
        CyclePeriodResultVO result = service.calculateQuarterCyclePeriod(timezone, currentTime, startTime, 1);
        printResult("1季度间隔", result, timezone);

        // 测试2季度间隔
        result = service.calculateQuarterCyclePeriod(timezone, currentTime, startTime, 2);
        printResult("2季度间隔", result, timezone);

        System.out.println();
    }

    private static void testYearCycle(CyclePeriodCalculateServiceImpl service, String timezone, long currentTime, long startTime) {
        System.out.println("=== 年周期测试 ===");

        // 测试1年间隔
        CyclePeriodResultVO result = service.calculateYearCyclePeriod(timezone, currentTime, startTime, 1);
        printResult("1年间隔", result, timezone);

        // 测试2年间隔
        result = service.calculateYearCyclePeriod(timezone, currentTime, startTime, 2);
        printResult("2年间隔", result, timezone);

        System.out.println();
    }

    private static void printResult(String testName, CyclePeriodResultVO result, String timezone) {
        System.out.println("--- " + testName + " ---");
        if (result.isSuccess()) {
            TimeZone tz = TimeZone.getTimeZone(timezone);
            DateTime startTime = DateUtil.date(result.getCycleStartTime()).setTimeZone(tz);
            DateTime endTime = DateUtil.date(result.getCycleEndTime()).setTimeZone(tz);

            System.out.println("周期索引: " + result.getCycleIndex());
            System.out.println("周期编号: " + result.getCycleNumber());
            System.out.println("周期开始: " + startTime);
            System.out.println("周期结束: " + endTime);
            System.out.println("开始时间戳: " + result.getCycleStartTime());
            System.out.println("结束时间戳: " + result.getCycleEndTime());
        } else {
            System.out.println("计算失败: " + result.getErrorMessage());
        }
        System.out.println();
    }
}
