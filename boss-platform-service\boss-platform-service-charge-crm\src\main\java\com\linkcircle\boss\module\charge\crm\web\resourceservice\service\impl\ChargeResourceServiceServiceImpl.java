package com.linkcircle.boss.module.charge.crm.web.resourceservice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.common.util.object.BeanUtils;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.module.charge.crm.enums.ChargeConfigBusProtoStatusEnum;
import com.linkcircle.boss.module.charge.crm.enums.ChargeResourceServiceStatusEnum;
import com.linkcircle.boss.module.charge.crm.enums.ChargeTypeEnum;
import com.linkcircle.boss.module.charge.crm.utils.VersionConverter;
import com.linkcircle.boss.module.charge.crm.web.base.scale.service.ChargeScaleInfoService;
import com.linkcircle.boss.module.charge.crm.web.purchase.mapper.ChargePurchaseServicePriceMapper;
import com.linkcircle.boss.module.charge.crm.web.purchase.model.ChargePurchaseServicePrice;
import com.linkcircle.boss.module.charge.crm.web.resource.mapper.ChargeResourceServiceConfigMapper;
import com.linkcircle.boss.module.charge.crm.web.resource.model.ChargeResourceServiceConfig;
import com.linkcircle.boss.module.charge.crm.web.resource.model.dto.VersionInfoListParam;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.mapper.ChargeResourceServiceMapper;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceEditDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceServicePrice;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.*;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.IChargeResourceServicePriceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.IChargeResourceServiceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.service.base.ChargeTypeStrategyManager;
import com.linkcircle.boss.module.crm.enums.ScaleTypeEnum;
import lombok.RequiredArgsConstructor;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.linkcircle.boss.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants.*;
import static com.linkcircle.boss.module.charge.crm.web.resourceservice.service.base.IChargeTypeService.FIRST_VERSION_CODE;

/**
 * <p>
 * 资源服务基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
@RequiredArgsConstructor
public class ChargeResourceServiceServiceImpl extends ServiceImpl<ChargeResourceServiceMapper, ChargeResourceService> implements IChargeResourceServiceService {

    private final ChargeResourceServiceMapper chargeResourceServiceMapper;

    private final ChargeTypeStrategyManager chargeTypeStrategyManager;

    private final IChargeResourceServicePriceService chargeResourceServicePriceService;

    private final ChargeResourceServiceConfigMapper chargeResourceServiceConfigMapper;

    private final ChargePurchaseServicePriceMapper chargePurchaseServicePriceMapper;

    private final ChargeScaleInfoService chargeScaleInfoService;

    @Override
    public PageResult<ChargeResourceServiceVO> pageQuery(ChargeResourceServiceQueryDTO queryDTO) {
        Page<?> page = MyBatisUtils.buildPage(queryDTO);
        List<ChargeResourceServiceVO> list = chargeResourceServiceMapper.pageQuery(page, queryDTO);
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> create(ChargeResourceServiceAddDTO addDTO) {
        //名称效验
        if (checkServiceName(addDTO.getServiceName())) {
            throw new ServiceException(RESOURCE_SERVICE_NAME_REPEATED_EXCEPTION);
        }
        ChargeResourceService chargeResourceService = new ChargeResourceService();
        BeanUtils.copyProperties(addDTO, chargeResourceService);
        chargeResourceService.setStatus(ChargeResourceServiceStatusEnum.unActivation.getCode());
        chargeResourceService.setServiceCode(convertToCode(addDTO.getServiceName()));

        if (ChargeTypeEnum.packageRate.getCode().equals(addDTO.getChargeType()) || ChargeTypeEnum.postpaidRate.getCode().equals(addDTO.getChargeType())) {
            chargeResourceService.setConfigBusProto(ChargeConfigBusProtoStatusEnum.NOT_CONFIGURED.getCode());
        } else {
            chargeResourceService.setConfigBusProto(ChargeConfigBusProtoStatusEnum.NOT_INVOLVE.getCode());
        }

        save(chargeResourceService);
        //版本数据
        ChargeResourceServicePrice chargeResourceServicePrice = new ChargeResourceServicePrice();
        BeanUtils.copyProperties(addDTO, chargeResourceServicePrice);
        //构建初始版本号
        chargeResourceServicePrice.setServiceId(chargeResourceService.getId());
        chargeResourceServicePrice.setVersionName(VersionConverter.toVersionName(FIRST_VERSION_CODE));
        chargeResourceServicePrice.setVersionCode(FIRST_VERSION_CODE);
        CommonResult<?> commonResult = chargeTypeStrategyManager.getStrategy(addDTO.getChargeType()).add(addDTO, chargeResourceServicePrice);
        if (!commonResult.isSuccess()) {
            throw new ServiceException(BAD_REQUEST.getCode(), commonResult.getMsg());
        }
        return CommonResult.success(chargeResourceService.getId());
    }

    /**
     * 将中文服务名转换为不限长度的英文编码
     *
     * @param serviceName 服务名称(可包含中文、英文、数字)
     * @return 大写英文编码
     */
    public static String convertToCode(String serviceName) {
        StringBuilder code = new StringBuilder();
        try {
            for (char c : serviceName.toCharArray()) {
                if (isChinese(c)) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        code.append(pinyinArray[0].charAt(0));
                    }
                } else {
                    code.append(c);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(BAD_REQUEST.getCode(), e.getMessage());
        }
        String timeStr = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        return code.toString().toUpperCase() + timeStr;
    }

    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A;
    }

    private boolean checkServiceName(String serviceName) {
        LambdaQueryWrapper<ChargeResourceService> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResourceService::getServiceName, serviceName);
        return count(queryWrapper) > 0;
    }


    @Override
    public CommonResult<?> addVersion(Long serviceId) {
        //最大版本
        ChargeResourceServicePrice maxChargeResourceServicePrice = chargeResourceServicePriceService.getMaxVersion(serviceId);
        int nextVersionCode = VersionConverter.nextVersionCode(maxChargeResourceServicePrice.getVersionCode());
        ChargeResourceServicePrice chargeResourceServicePrice = new ChargeResourceServicePrice();
        BeanUtils.copyProperties(maxChargeResourceServicePrice, chargeResourceServicePrice);
        chargeResourceServicePrice.setVersionName(VersionConverter.toVersionName(nextVersionCode));
        chargeResourceServicePrice.setVersionCode(nextVersionCode);
        chargeResourceServicePrice.setId(null);
        chargeResourceServicePriceService.save(chargeResourceServicePrice);
        return CommonResult.success(chargeResourceServicePrice.getId());
    }

    @Override
    public CommonResult<?> edit(ChargeResourceServiceEditDTO editDTO) {
        ChargeResourceServicePrice chargeResourceServicePrice = new ChargeResourceServicePrice();
        chargeResourceServicePrice.setId(editDTO.getVersionId());
        BeanUtils.copyProperties(editDTO, chargeResourceServicePrice);
        return chargeTypeStrategyManager.getStrategy(editDTO.getChargeType()).edit(editDTO, chargeResourceServicePrice);
    }

    @Override
    public CommonResult<?> activation(List<Long> serviceIds) {
        //是否配置量表
        List<ChargeResourceService> chargeResourceServiceList = chargeResourceServiceMapper.selectByIds(serviceIds);
        List<Long> failId = new ArrayList<>();
        chargeResourceServiceList.forEach(chargeResourceService -> {
            Integer chargeType = chargeResourceService.getChargeType();
            if (chargeType.equals(ChargeTypeEnum.packageRate.getCode()) || chargeType.equals(ChargeTypeEnum.postpaidRate.getCode())) {
                if (ObjectUtil.isNull(chargeResourceService.getScaleId())) {
                    failId.add(chargeResourceService.getId());
                }
            }
        });
        if (!failId.isEmpty()) {
            return CommonResult.error(RESOURCE_SERVICE_SCALE_UNCONFIGURED_EXCEPTION.getCode(), CollUtil.join(failId, StrUtil.COMMA) + "请配置量表");
        }
        LambdaUpdateWrapper<ChargeResourceService> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResourceService::getId, serviceIds);
        updateWrapper.set(ChargeResourceService::getStatus, ChargeResourceServiceStatusEnum.activation.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    public CommonResult<?> deactivate(List<Long> serviceIds) {
        LambdaUpdateWrapper<ChargeResourceService> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResourceService::getId, serviceIds);
        updateWrapper.set(ChargeResourceService::getStatus, ChargeResourceServiceStatusEnum.unActivation.getCode());
        List<Long> allVersionId = getAllVersionId(serviceIds);
        //更新资源的服务
        LambdaUpdateWrapper<ChargeResourceServiceConfig> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.in(ChargeResourceServiceConfig::getServicePriceId, allVersionId);
        chargeResourceServiceConfigMapper.delete(deleteWrapper);
        return CommonResult.success(update(updateWrapper));
    }

    private List<Long> getAllVersionId(List<Long> serviceIds) {
        LambdaQueryWrapper<ChargeResourceServicePrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChargeResourceServicePrice::getServiceId, serviceIds);
        List<ChargeResourceServicePrice> chargeResourceServicePriceList = chargeResourceServicePriceService.list(queryWrapper);
        return chargeResourceServicePriceList.stream().map(ChargeResourceServicePrice::getId).collect(Collectors.toList());
    }

    @Override
    public CommonResult<?> archive(List<Long> serviceIds) {
        //确认是否被采购
        LambdaQueryWrapper<ChargePurchaseServicePrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChargePurchaseServicePrice::getResourceServiceId, serviceIds);
        List<ChargePurchaseServicePrice> chargePurchaseServicePriceList = chargePurchaseServicePriceMapper.selectList(queryWrapper);

        List<Long> purchaseIds = chargePurchaseServicePriceList.stream().map(ChargePurchaseServicePrice::getResourceServiceId).toList();
        if (CollUtil.isNotEmpty(purchaseIds)) {
            return CommonResult.error(RESOURCE_SERVICE_BE_PURCHASE_EXCEPTION.getCode(), CollUtil.join(purchaseIds, StrUtil.COMMA) + "已被采购");
        }
        LambdaUpdateWrapper<ChargeResourceService> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResourceService::getId, serviceIds);
        updateWrapper.set(ChargeResourceService::getStatus, ChargeResourceServiceStatusEnum.archive.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    public CommonResult<?> cancelArchive(List<Long> serviceIds) {
        LambdaUpdateWrapper<ChargeResourceService> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ChargeResourceService::getId, serviceIds);
        updateWrapper.set(ChargeResourceService::getStatus, ChargeResourceServiceStatusEnum.unActivation.getCode());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> delete(List<Long> serviceIds) {
        removeBatchByIds(serviceIds);
        LambdaQueryWrapper<ChargeResourceServicePrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChargeResourceServicePrice::getServiceId, serviceIds);
        chargeResourceServicePriceService.remove(queryWrapper);
        return CommonResult.success();
    }

    @Override
    public CommonResult<?> configScale(Long serviceId, Long scaleId) {
        ChargeResourceService chargeResourceService = chargeResourceServiceMapper.selectById(serviceId);
        Integer chargeType = chargeResourceService.getChargeType();
        if (chargeType.equals(ChargeTypeEnum.fixeRate.getCode()) || chargeType.equals(ChargeTypeEnum.ladderRate.getCode())) {
            return CommonResult.error(RESOURCE_SERVICE_UN_NEED_CONFIGURED_EXCEPTION);
        }
        LambdaUpdateWrapper<ChargeResourceService> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChargeResourceService::getId, serviceId);
        updateWrapper.set(ChargeResourceService::getScaleId, scaleId);
        updateWrapper.set(ChargeResourceService::getConfigBusProto, ChargeConfigBusProtoStatusEnum.CONFIGURED.getCode());
        chargeScaleInfoService.createScaleTable(chargeResourceService.getServiceCode(), scaleId, ScaleTypeEnum.RESOURCE.getType());
        return CommonResult.success(update(updateWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> copy(Long serviceId) {
        ChargeResourceService chargeResourceService = chargeResourceServiceMapper.selectById(serviceId);
        chargeResourceService.setId(null);
        String suffix = "_copy";
        String serviceName = chargeResourceService.getServiceName() + suffix;
        Integer size = getSizeByServiceName(serviceName);
        if (size > 0) {
            serviceName = serviceName + suffix + size;
        } else {
            serviceName = serviceName + suffix + 1;
        }
        chargeResourceService.setServiceName(serviceName);
        save(chargeResourceService);
        copyMaxVersion(serviceId, chargeResourceService.getId());
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<ChargeResourceServiceVersionVO>> getVersionList(Long serviceId) {
        List<ChargeResourceServiceVersionVO> chargeResourceServiceVersionVOS = chargeResourceServiceMapper.getVersionList(serviceId);
        return CommonResult.success(chargeResourceServiceVersionVOS);
    }

    @Override
    public CommonResult<ChargeResourceServiceVersionInfoVO> getVersionInfoById(Long versionId) {
        List<ChargeResourceServiceVersionInfoVO> chargeResourceServiceVersionInfoVOList = chargeResourceServiceMapper.getVersionInfoById(versionId, null);
        if (chargeResourceServiceVersionInfoVOList.isEmpty()) {
            return CommonResult.success(null);
        }
        return CommonResult.success(chargeResourceServiceVersionInfoVOList.getFirst());
    }

    @Override
    public CommonResult<ChargeResourceServiceVersionInfoVO> getMaxVersionInfoById(Long serviceId) {
        ChargeResourceServiceVersionInfoVO chargeResourceServiceVersionInfo = chargeResourceServiceMapper.getMaxVersionInfoById(serviceId);
        return CommonResult.success(chargeResourceServiceVersionInfo);
    }

    @Override
    public CommonResult<?> editVersionName(Long versionId, String versionName) {
        LambdaUpdateWrapper<ChargeResourceServicePrice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChargeResourceServicePrice::getId, versionId);
        updateWrapper.set(ChargeResourceServicePrice::getVersionName, versionName);
        return CommonResult.success(chargeResourceServicePriceService.update(updateWrapper));
    }

    @Override
    public CommonResult<List<ChargeResourceServiceGroupVO>> getVersionInfoListByName(String serviceName) {
        List<ChargeResourceServiceVersionInfoVO> chargeResourceServiceVersionInfoVOList = chargeResourceServiceMapper.getVersionInfoListByParam(new VersionInfoListParam().setServiceName(serviceName));
        return CommonResult.success(chargeResourceServiceVersionInfoVOList.stream()
                .collect(Collectors.groupingBy(
                        ChargeResourceServiceVersionInfoVO::getServiceName
                ))
                .entrySet().stream()
                .map(entry -> new ChargeResourceServiceGroupVO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    protected void copyMaxVersion(Long serviceId, Long newServiceId) {
        LambdaQueryWrapper<ChargeResourceServicePrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResourceServicePrice::getServiceId, serviceId)
                .orderByDesc(ChargeResourceServicePrice::getVersionCode)
                .last("LIMIT 1");
        ChargeResourceServicePrice chargeResourceServicePrice = chargeResourceServicePriceService.getOne(queryWrapper);
        chargeResourceServicePrice.setId(null);
        chargeResourceServicePrice.setServiceId(newServiceId);
        chargeResourceServicePrice.setVersionCode(FIRST_VERSION_CODE);
        chargeResourceServicePrice.setVersionName(VersionConverter.toVersionName(chargeResourceServicePrice.getVersionCode()));
        chargeResourceServicePriceService.save(chargeResourceServicePrice);
    }

    private Integer getSizeByServiceName(String serviceName) {
        LambdaQueryWrapper<ChargeResourceService> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ChargeResourceService::getServiceName, serviceName);
        List<ChargeResourceService> chargeResourceServiceList = chargeResourceServiceMapper.selectList(queryWrapper);
        return chargeResourceServiceList.size();
    }

    @Override
    public CommonResult<List<ChargeResourceServiceVersionInfoVO>> getResourceServiceByCurrencyCode(String currencyCode) {
        List<ChargeResourceServiceVersionInfoVO> chargeResourceServiceVersionVOS = chargeResourceServiceMapper.getVersionInfoListByParam(new VersionInfoListParam().setCurrencyCode(currencyCode));
        return CommonResult.success(chargeResourceServiceVersionVOS);
    }

    @Override
    public CommonResult<List<ChargeResourceServiceDropDownVO>> getAllResourceService() {
        LambdaQueryWrapper<ChargeResourceService> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChargeResourceService::getStatus, ChargeResourceServiceStatusEnum.activation.getCode());
        List<ChargeResourceService> chargeResourceServiceList = chargeResourceServiceMapper.selectList(queryWrapper);
        return CommonResult.success(chargeResourceServiceList.stream().map(chargeResourceService -> new ChargeResourceServiceDropDownVO(chargeResourceService.getId(), chargeResourceService.getServiceName())).toList());
    }

}
