package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.billing.web.manager.MetricUnitConverter;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.enums.DiscountRangeEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-27 14:54
 * @description
 */
@Slf4j
public abstract class AbstractOriginalPriceStrategy {

    public static final long DAY_MILLS = 24 * 60 * 60 * 1000;


    /**
     * 按比例计算
     * 根据实际使用时间占当前周期总时间的比例来计算费用
     * <p>
     * 示例：
     * 固定费率：30元
     * 出账周期：7月1日～7月31日
     * 用户订阅时间：7月15日
     * 实际使用时间：7月15日至7月31日，共17天
     * 当月天数：31天
     * 占比：17 / 31 ≈ 0.548
     * 费用 = 30元 × 0.548 ≈ 16.44元
     *
     * @param request 计算请求
     */
    protected void calculateProportion(OriginalPriceCalculateRequest request,
                                       OriginalPriceCalculateResponse response) {
        if (!request.isByProportion()) {
            return;
        }
        Long startTime = request.getStartTime();
        Long endTime = request.getEndTime();
        Long cycleStartTime = request.getCycleStartTime();
        Long cycleEndTime = request.getCycleEndTime();

        // 参数校验
        if (startTime == null || endTime == null || cycleStartTime == null || cycleEndTime == null) {
            log.warn("按比例计算参数不完整，使用默认比例1.0");
            return;
        }

        // 确定实际使用的开始和结束时间
        long actualStartTime = Math.max(startTime, cycleStartTime);
        long actualEndTime = Math.min(endTime, cycleEndTime);

        // 如果实际使用时间无效，返回0
        if (actualStartTime >= actualEndTime) {
            log.warn("实际使用时间无效，actualStartTime: {}, actualEndTime: {}", actualStartTime, actualEndTime);
            return;
        }

        // 转换为LocalDate进行天数计算
        LocalDate actualStartDate = Instant.ofEpochMilli(actualStartTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate actualEndDate = Instant.ofEpochMilli(actualEndTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate cycleStartDate = Instant.ofEpochMilli(cycleStartTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate cycleEndDate = Instant.ofEpochMilli(cycleEndTime).atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算实际使用天数（包含开始日期，不包含结束日期）
        long actualUsageDays = ChronoUnit.DAYS.between(actualStartDate, actualEndDate);
        if (actualUsageDays == 0) {
            // 如果是同一天，按1天计算
            actualUsageDays = 1;
        }

        // 计算周期总天数（包含开始日期，不包含结束日期）
        long totalCycleDays = ChronoUnit.DAYS.between(cycleStartDate, cycleEndDate);
        if (totalCycleDays == 0) {
            // 如果是同一天，按1天计算
            totalCycleDays = 1;
        }

        // 计算比例，保留4位小数
        BigDecimal proportion = BigDecimal.valueOf(actualUsageDays)
                .divide(BigDecimal.valueOf(totalCycleDays), 4, RoundingMode.HALF_UP);

        log.info("按比例计算详情 - 实际使用天数: {}, 周期总天数: {}, 比例: {}",
                actualUsageDays, totalCycleDays, proportion);

        response.setOriginalPrice(response.getOriginalPrice().multiply(proportion));
        response.setDiscountedPrice(response.getDiscountedPrice().multiply(proportion));
    }

    /**
     * 税率计算
     */
    protected void calculateTax(OriginalPriceCalculateRequest request, OriginalPriceCalculateResponse response) {
        if (!Boolean.TRUE.equals(request.getCalculateTaxEnabled())) {
            return;
        }
        BigDecimal taxRate = request.getTaxRate();
        BigDecimal discountedPrice = response.getDiscountedPrice();
        response.setTaxRate(taxRate);
        if (taxRate == null || taxRate.compareTo(BigDecimal.ZERO) <= 0) {
            response.setTaxAmount(BigDecimal.ZERO);
            response.setAmountWithTax(discountedPrice);
            response.setAmountWithoutTax(discountedPrice);
            return;
        }

        BigDecimal taxAmount = calculateTaxAmount(discountedPrice, taxRate);
        BigDecimal amountWithTax = discountedPrice.add(taxAmount);
        response.setTaxAmount(taxAmount);
        response.setAmountWithTax(amountWithTax);
        response.setAmountWithoutTax(discountedPrice);
    }

    /**
     * 计算税额
     *
     * @param taxableAmount 应税金额
     * @param taxRate       税率
     * @return 税额
     */
    protected BigDecimal calculateTaxAmount(BigDecimal taxableAmount, BigDecimal taxRate) {
        if (taxRate == null || taxRate.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return taxableAmount.multiply(taxRate.divide(BigDecimal.valueOf(100)).setScale(4, RoundingMode.HALF_UP))
                .setScale(6, RoundingMode.HALF_UP);
    }


    /**
     * 试用期
     */
    protected void inTrial(OriginalPriceCalculateRequest request, OriginalPriceCalculateResponse response) {
        Integer freeTryoutDays = request.getFreeTryoutDays();
        response.setInTrail(false);
        if (Objects.nonNull(freeTryoutDays) && freeTryoutDays > 0) {
            Long startTime = request.getStartTime();
            response.setStartTime(startTime);
            response.setFreeTryoutDays(freeTryoutDays);
            response.setCurrentTime(System.currentTimeMillis());
            if (startTime != null && startTime > 0) {
                long currentTime = System.currentTimeMillis();
                if (currentTime < startTime + freeTryoutDays * DAY_MILLS) {
                    response.setInTrail(true);
                    response.setDiscountedPrice(BigDecimal.ZERO);
                    response.setOriginalPrice(BigDecimal.ZERO);
                }
            }
        }
    }

    protected BigDecimal convertUnit(BigDecimal value, String fromUnit, String toUnit) {
        MetricUnitConverter.ConvertResult convertResult = MetricUnitConverter.convert(value, fromUnit, toUnit);
        BigDecimal actualCurrentUsage = convertResult.getConvertedValue();
        log.info("用量单位转化, 原始值: {}, 换算值: {}", convertResult.originDisplayValue(), convertResult.getDisplayValue());
        return actualCurrentUsage;
    }

    /**
     * 向上取整
     */
    protected BigDecimal roundUnit(BigDecimal from, BigDecimal to, Integer measureCeil) {
        if (Objects.nonNull(measureCeil) && measureCeil == 1) {
            return from.divide(to, 0, RoundingMode.CEILING);
        }
        return from.divide(to, 0, RoundingMode.DOWN);
    }

    /**
     * 根据支付类型 获取单价
     * 支付方式 0-现金 1-积分
     */
    protected BigDecimal getUnitPrice(Integer paymentOptions, BigDecimal cash, BigDecimal point) {
        if (paymentOptions != null && paymentOptions == 1) {
            return point;
        }
        return cash;
    }

    /**
     * 算总价
     */
    protected BigDecimal getTotalPrice(Integer paymentOptions, BigDecimal units, BigDecimal unitPrice) {
        if (paymentOptions != null && paymentOptions == 0) {
            return units.multiply(unitPrice).setScale(6, RoundingMode.HALF_UP);
        }
        return units.multiply(unitPrice);
    }

    /**
     * 计算优惠价
     *
     * @param originalPrice 目录价
     * @param couponList    优惠信息
     * @return 优惠金额
     */
    protected BigDecimal calculateDiscountPrice(BigDecimal originalPrice, boolean inPackage, List<Coupon> couponList) {
        if (CollUtil.isEmpty(couponList)) {
            return originalPrice;
        }

        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (Coupon coupon : couponList) {
            Integer couponScope = coupon.getScope();
            if (inPackage && !Objects.equals(DiscountRangeEnum.INNER.getCode(), couponScope)) {
                continue;
            }

            if (!inPackage && !Objects.equals(DiscountRangeEnum.OUTER.getCode(), couponScope)) {
                continue;
            }

            BigDecimal discountAmount = calculateSingleCouponDiscount(originalPrice, coupon);
            coupon.setDiscountAmount(discountAmount);
            totalDiscount = totalDiscount.add(discountAmount);
        }
        BigDecimal price = totalDiscount.setScale(6, RoundingMode.HALF_UP);
        // 比0小 返回0
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        }
        return price;
    }

    /**
     * 计算优惠价
     *
     * @param originalPrice 目录价
     * @param couponList    优惠信息
     * @return 优惠金额
     */
    protected BigDecimal calculateDiscountPrice(BigDecimal originalPrice, List<Coupon> couponList) {
        if (CollUtil.isEmpty(couponList)) {
            return originalPrice;
        }

        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (Coupon coupon : couponList) {
            BigDecimal discountAmount = calculateSingleCouponDiscount(originalPrice, coupon);
            coupon.setDiscountAmount(discountAmount);
            totalDiscount = totalDiscount.add(discountAmount);
        }
        BigDecimal price = totalDiscount.setScale(6, RoundingMode.HALF_UP);
        // 比0小 返回0
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        }
        return price;
    }

    /**
     * 计算单个优惠券的优惠金额
     *
     * @param originalPrice 目录价
     * @param coupon        优惠券
     * @return 优惠金额
     */
    protected BigDecimal calculateSingleCouponDiscount(BigDecimal originalPrice, Coupon coupon) {
        if (coupon.getCouponType() == null) {
            return BigDecimal.ZERO;
        }

        // 固定金额优惠：直接返回优惠固定金额
        if (coupon.getCouponType() == 0) {
            if (coupon.getCouponAmount() != null && coupon.getCouponAmount().compareTo(BigDecimal.ZERO) > 0) {
                return coupon.getCouponAmount();
            }
            return BigDecimal.ZERO;
        }

        // 按百分比优惠：目录价乘以百分比得到优惠金额
        if (coupon.getCouponType() == 1) {
            if (coupon.getCouponPercentage() != null && coupon.getCouponPercentage().compareTo(BigDecimal.ZERO) > 0) {
                return originalPrice.multiply(coupon.getCouponPercentage().divide(BigDecimal.valueOf(100)))
                        .setScale(6, RoundingMode.HALF_UP);
            }
            return BigDecimal.ZERO;
        }

        return BigDecimal.ZERO;
    }

}
