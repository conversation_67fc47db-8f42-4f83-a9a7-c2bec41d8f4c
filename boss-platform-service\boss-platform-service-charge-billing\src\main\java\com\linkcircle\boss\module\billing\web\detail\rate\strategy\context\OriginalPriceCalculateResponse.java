package com.linkcircle.boss.module.billing.web.detail.rate.strategy.context;

import cn.hutool.core.text.StrFormatter;
import com.baomidou.mybatisplus.annotation.TableField;
import com.linkcircle.boss.framework.common.exception.ErrorCode;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 原价计算响应
 */
@Data
@Schema(description = "原价计算响应")
public class OriginalPriceCalculateResponse {

    /**
     * 费率计算结果
     */
    private Object rateConfig;

    /**
     * 优惠计算结果
     */
    private List<Coupon> couponList;

    @Schema(description = "税率")
    private BigDecimal taxRate;

    @Schema(description = "税额")
    private BigDecimal taxAmount;

    @Schema(description = "含税总金额")
    private BigDecimal amountWithTax;

    @Schema(description = "不含税金额")
    private BigDecimal amountWithoutTax;

    @Schema(description = "计费单位数")
    private BigDecimal chargeUnitCount;

    @Schema(description = "使用量")
    private BigDecimal usage;

    @Schema(description = "使用量单位")
    private String usageUnit;

    @Schema(description = "订阅单价")
    private BigDecimal discountedUnitPrice;

    @Schema(description = "目录单价")
    private BigDecimal originalUnitPrice;

    @Schema(description = "目录总价(单价原价)")
    private BigDecimal originalPrice;

    @Schema(description = "订阅总价(优惠的目录价)")
    private BigDecimal discountedPrice;

    @TableField("discount_amount")
    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "计量")
    private BigDecimal measure;

    @Schema(description = "计量单位")
    private String measureUnit;

    @Schema(description = "计量单位是否向上取整 0-不向上取整, 1-向上取整")
    private Integer measureCeil;

    @TableField("charge_usage_count")
    @Schema(description = "计费消耗量(measure*charge_unit_count)", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal chargeUsageCount;

    @Schema(description = "是否 试用期")
    private Boolean inTrail;

    @Schema(description = "试用天数")
    private Integer freeTryoutDays;

    @Schema(description = "订阅开始时间")
    private Long startTime;

    @Schema(description = "订阅结束时间")
    private Long endTime;

    @Schema(description = "当前周期开始时间")
    private Long cycleStartTime;

    @Schema(description = "当前周期结束时间")
    private Long cycleEndTime;

    @Schema(description = "是否按比例计算")
    private Integer byProportion;

    @Schema(description = "当前时间")
    private Long currentTime;

    @Schema(description = "是否计算成功")
    private Boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "错误码")
    private Integer errorCode;

    /**
     * 创建失败的响应
     *
     * @param errorCode 错误码
     * @return 失败响应
     */
    public static OriginalPriceCalculateResponse fail(ErrorCode errorCode) {
        OriginalPriceCalculateResponse response = new OriginalPriceCalculateResponse();
        response.setSuccess(false);
        response.setErrorCode(errorCode.getCode());
        response.setErrorMessage(errorCode.getMsg());
        return response;
    }

    /**
     * 创建失败的响应（带参数）
     *
     * @param errorCode 错误码
     * @param args      错误信息参数
     * @return 失败响应
     */
    public static OriginalPriceCalculateResponse fail(ErrorCode errorCode, Object... args) {
        OriginalPriceCalculateResponse response = new OriginalPriceCalculateResponse();
        response.setSuccess(false);
        response.setErrorCode(errorCode.getCode());
        response.setErrorMessage(StrFormatter.format(errorCode.getMsg(), args));
        return response;
    }

    /**
     * 创建成功的响应
     *
     * @return 成功响应
     */
    public static OriginalPriceCalculateResponse success() {
        OriginalPriceCalculateResponse response = new OriginalPriceCalculateResponse();
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建成功的响应（带原价）
     *
     * @param originalPrice 原价
     * @return 成功响应
     */
    public static OriginalPriceCalculateResponse success(BigDecimal originalPrice) {
        OriginalPriceCalculateResponse response = success();
        response.setOriginalUnitPrice(originalPrice);
        return response;
    }

}
