package com.linkcircle.boss.module.billing.web.manager;

import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:25
 * @description 账单ID管理器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BillIdManager {

    private final RedissonClient redissonClient;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddhhmm");

    public Long createBillIdLong(BillTypeEnum billType, Long time) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneOffset.UTC);
        return Long.parseLong(createBillId(billType, dateTime));
    }

    public String createBillIdNow(BillTypeEnum billType) {
        LocalDateTime utcNow = LocalDateTime.now(ZoneOffset.UTC);
        return createBillId(billType, utcNow);
    }

    /**
     * 生成账单ID
     * 格式：yyyyMMddHHmm(UTC时间) + 5位递增序号
     *
     * @param billType 账单类型（预留参数，可用于区分不同类型的账单）
     * @return 账单ID
     */
    public String createBillId(BillTypeEnum billType, LocalDateTime dateTime) {
        try {
            // 获取UTC时间
            String dateHourStr = dateTime.format(DATE_TIME_FORMATTER);

            // 获取当日小时序号
            String sequenceKey = ChargeCacheUtils.getBillIdSequenceKey(billType.name().toLowerCase(), dateHourStr);
            RAtomicLong atomicLong = redissonClient.getAtomicLong(sequenceKey);
            long sequence = atomicLong.incrementAndGet();
            if (sequence == 1) {
                atomicLong.expire(Duration.ofMinutes(10));
            }

            // 格式化序号为5位数字，不足补0
            String sequenceStr = String.format("%06d", sequence % 100000);

            String billId = billType.getType() + dateHourStr + sequenceStr;
            log.debug("生成账单ID: {}, type: {}, sequence: {}", billId, billType, sequence);
            return billId;
        } catch (Exception e) {
            log.error("生成账单ID失败, type: {}", billType, e);
            // 降级方案：使用时间戳 + 随机数
            return System.currentTimeMillis() + String.format("%05d", (int) (Math.random() * 100000));
        }
    }
}
