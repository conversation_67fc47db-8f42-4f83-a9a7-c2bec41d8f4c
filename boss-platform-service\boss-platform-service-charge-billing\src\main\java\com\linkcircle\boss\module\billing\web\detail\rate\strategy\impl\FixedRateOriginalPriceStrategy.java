package com.linkcircle.boss.module.billing.web.detail.rate.strategy.impl;

import com.linkcircle.boss.module.billing.api.rate.model.dto.FixedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 固定费率原价计算策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = OriginalPriceRateTypeEnum.class, strategyType = RateTypeConstant.FIXED)
public class FixedRateOriginalPriceStrategy extends AbstractOriginalPriceStrategy implements IStrategy<OriginalPriceCalculateRequest, OriginalPriceCalculateResponse> {

    @Override
    public OriginalPriceCalculateResponse execute(OriginalPriceCalculateRequest request) {
        FixedRateConfigDTO rateConfig = (FixedRateConfigDTO) request.getRateConfig();
        Integer paymentOptions = request.getPaymentOptions();
        BigDecimal currentUsage = request.getCurrentUsage();

        // 目录价
        BigDecimal originalUnitPrice = getUnitPrice(paymentOptions, rateConfig.getFixCharge(), BigDecimal.valueOf(rateConfig.getIntegralCharge()));

        OriginalPriceCalculateResponse response = OriginalPriceCalculateResponse.success(originalUnitPrice);
        response.setMeasure(BigDecimal.ONE);
        response.setMeasureCeil(0);

        // 优惠价
        BigDecimal discountUnitPrice = calculateDiscountPrice(originalUnitPrice, request.getCouponList());
        response.setDiscountedUnitPrice(discountUnitPrice);
        response.setDiscountedPrice(currentUsage.multiply(discountUnitPrice));

        response.setUsage(currentUsage);
        response.setChargeUnitCount(currentUsage);
        response.setChargeUsageCount(currentUsage);
        response.setOriginalPrice(currentUsage.multiply(originalUnitPrice));

        rateConfig.setOriginalUnitPrice(originalUnitPrice);
        rateConfig.setDiscountedUnitPrice(discountUnitPrice);
        rateConfig.setOriginalPrice(response.getOriginalPrice());
        rateConfig.setDiscountedPrice(response.getDiscountedPrice());
        rateConfig.setChargeUnitCount(currentUsage);

        calculateTax(request, response);

        response.setRateConfig(rateConfig);
        response.setCouponList(request.getCouponList());
        calculateProportion(request, response);
        
        response.setDiscountAmount(response.getOriginalPrice().subtract(response.getDiscountedPrice()));
        return response;
    }


    /**
     * 按比例计算
     * 根据实际使用时间占当前周期总时间的比例来计算费用
     * <p>
     * 示例：
     * 固定费率：30元
     * 出账周期：7月1日～7月31日
     * 用户订阅时间：7月15日
     * 实际使用时间：7月15日至7月31日，共17天
     * 当月天数：31天
     * 占比：17 / 31 ≈ 0.548
     * 费用 = 30元 × 0.548 ≈ 16.44元
     *
     * @param request 计算请求
     */
    public void calculateProportion(OriginalPriceCalculateRequest request,
                                     OriginalPriceCalculateResponse response) {
        if (!request.isByProportion()) {
            return;
        }
        Long startTime = request.getStartTime();
        Long endTime = request.getEndTime();
        Long cycleStartTime = request.getCycleStartTime();
        Long cycleEndTime = request.getCycleEndTime();

        // 参数校验
        if (startTime == null || endTime == null || cycleStartTime == null || cycleEndTime == null) {
            log.warn("按比例计算参数不完整，使用默认比例1.0");
            return;
        }

        // 确定实际使用的开始和结束时间
        long actualStartTime = Math.max(startTime, cycleStartTime);
        long actualEndTime = Math.min(endTime, cycleEndTime);

        // 如果实际使用时间无效，返回0
        if (actualStartTime >= actualEndTime) {
            log.warn("实际使用时间无效，actualStartTime: {}, actualEndTime: {}", actualStartTime, actualEndTime);
            return;
        }

        // 转换为LocalDate进行天数计算
        LocalDate actualStartDate = Instant.ofEpochMilli(actualStartTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate actualEndDate = Instant.ofEpochMilli(actualEndTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate cycleStartDate = Instant.ofEpochMilli(cycleStartTime).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate cycleEndDate = Instant.ofEpochMilli(cycleEndTime).atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算实际使用天数（包含开始日期，不包含结束日期）
        long actualUsageDays = ChronoUnit.DAYS.between(actualStartDate, actualEndDate);
        if (actualUsageDays == 0) {
            // 如果是同一天，按1天计算
            actualUsageDays = 1;
        }

        // 计算周期总天数（包含开始日期，不包含结束日期）
        long totalCycleDays = ChronoUnit.DAYS.between(cycleStartDate, cycleEndDate);
        if (totalCycleDays == 0) {
            // 如果是同一天，按1天计算
            totalCycleDays = 1;
        }

        // 计算比例，保留4位小数
        BigDecimal proportion = BigDecimal.valueOf(actualUsageDays)
                .divide(BigDecimal.valueOf(totalCycleDays), 4, RoundingMode.HALF_UP);

        log.info("按比例计算详情 - 实际使用天数: {}, 周期总天数: {}, 比例: {}",
                actualUsageDays, totalCycleDays, proportion);

        response.setOriginalPrice(response.getOriginalPrice().multiply(proportion));
        response.setDiscountedPrice(response.getDiscountedPrice().multiply(proportion));
    }
}
