package com.linkcircle.boss.module.charge.crm.web.supplier.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025/7/2 10:42
 */
public interface FunctionService<R, U> {

    Map<String, R> queryByIds(List<Long> ids);

    U convert(Long id, R r);

    default List<Long> strToLongList(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            return Arrays.stream(ids.split(",", -1)).filter(StringUtils::isNotBlank).map(l -> {
                try {
                    return Long.parseLong(l);
                } catch (Exception e) {
                    return null;
                }
            }).filter(Objects::nonNull).toList();
        }
        return List.of();
    }

    default <T> void handle(List<T> list, Function<T, String> mapper, BiConsumer<T, List<U>> consumer) {
        // TODO: 实现具体逻辑
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> ids = list.stream().map(t -> strToLongList(mapper.apply(t))).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).distinct().toList();
            if (CollectionUtils.isNotEmpty(ids)) {
                // TODO: 调用其他服务
                Map<String, R> map = queryByIds(ids);
                if (MapUtils.isNotEmpty(map)) {
                    for (T t : list) {
                        List<Long> ids1 = strToLongList(mapper.apply(t));
                        if (CollectionUtils.isNotEmpty(ids1)) {
                            List<U> us = ids1.stream().filter(Objects::nonNull).filter(f->map.containsKey(f.toString())).map(f -> convert(f, map.get(f.toString()))).filter(Objects::nonNull).toList();
                            if (CollectionUtils.isNotEmpty(us)) {
                                consumer.accept(t, us);
                            }
                        }
                    }
                }
            }
        }
    }

    default <T> void handle(T instance, Function<T, String> mapper, BiConsumer<T, List<U>> consumer) {
        // TODO: 实现具体逻辑
        if (instance != null) {
            List<Long> ids = strToLongList(mapper.apply(instance)).stream().distinct().toList();
            if (CollectionUtils.isNotEmpty(ids)) {
                // TODO: 调用其他服务
                Map<String, R> map = queryByIds(ids);
                if (MapUtils.isNotEmpty(map)) {
                    List<U> us = ids.stream().filter(Objects::nonNull).filter(f->map.containsKey(f.toString())).map(f -> convert(f, map.get(f.toString()))).filter(Objects::nonNull).toList();
                    if (CollectionUtils.isNotEmpty(us)) {
                        consumer.accept(instance, us);
                    }
                }
            }
        }
    }
}
