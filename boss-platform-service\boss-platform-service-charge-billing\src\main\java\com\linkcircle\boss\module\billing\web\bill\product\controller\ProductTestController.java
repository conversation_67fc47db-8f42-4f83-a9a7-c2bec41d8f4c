package com.linkcircle.boss.module.billing.web.bill.product.controller;

import com.google.common.collect.Lists;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.sharding.cache.ActualTableNameCache;
import com.linkcircle.boss.framework.sharding.core.TableMetadataRefresher;
import com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.IncomePostpaidServiceBillingScheduledTask;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.shardingsphere.infra.database.core.GlobalDataSourceRegistry;
import org.apache.shardingsphere.infra.database.core.metadata.data.loader.type.SchemaMetaDataLoader;
import org.apache.shardingsphere.infra.database.core.metadata.data.loader.type.TableMetaDataLoader;
import org.apache.shardingsphere.infra.database.core.metadata.data.model.TableMetaData;
import org.apache.shardingsphere.infra.database.core.type.DatabaseType;
import org.apache.shardingsphere.infra.spi.type.typed.TypedSPILoader;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-09 15:25
 * @description
 */
@RestController
@RequestMapping("/product-test")
@RequiredArgsConstructor
public class ProductTestController {

    private final IncomePostpaidServiceBillingScheduledTask incomePostpaidServiceBillingScheduledTask;
    private final TableMetadataRefresher tableMetadataRefresher;

    @PostMapping("/incomePostpaidSubscriptionBillingHandler")
    public String test() {
        incomePostpaidServiceBillingScheduledTask.incomePostpaidServiceBillingHandler();
        return "success";
    }

    // refresh
    @PostMapping("/refresh")
    public List<String> refresh() {
        tableMetadataRefresher.refreshMetadata();
        return ActualTableNameCache.all();
    }

    // test data
    @SneakyThrows
    @PostMapping("/test")
    public Map<String, Collection<String>> testData() {
        Map<String, DataSource> cachedDataSources = GlobalDataSourceRegistry.getInstance().getCachedDataSources();
        DataSource dataSource = cachedDataSources.get("cdr_0");
        DatabaseType databaseType = TypedSPILoader.getService(DatabaseType.class, "PostgreSQL");
        Map<String, Collection<String>> tableNames = SchemaMetaDataLoader.loadSchemaTableNames("cdr",
                databaseType, dataSource, Lists.newArrayList());
        Collection<String> strings = SchemaMetaDataLoader.loadSchemaNames(dataSource.getConnection(), databaseType);
        Collection<String> cdr = loadTableNames(dataSource.getConnection(), "cdr", Lists.newArrayList());
        System.out.println(cdr);
        System.out.println(JsonUtils.toJsonString(strings));
        System.out.println(tableNames);

        Optional<TableMetaData> load = TableMetaDataLoader.load(dataSource, ".*", databaseType);
        System.out.println(load);
        return tableNames;
    }

    private static final String TABLE_TYPE = "TABLE";

    private static final String VIEW_TYPE = "VIEW";

    private static final String SYSTEM_TABLE_TYPE = "SYSTEM TABLE";

    private static final String SYSTEM_VIEW_TYPE = "SYSTEM VIEW";

    private static final String TABLE_NAME = "TABLE_NAME";

    private static final String TABLE_SCHEME = "TABLE_SCHEM";

    private static Collection<String> loadTableNames(final Connection connection, final String schemaName, final Collection<String> excludedTables) throws SQLException {
        Collection<String> result = new LinkedList<>();
        try (ResultSet resultSet = connection.getMetaData().getTables(connection.getCatalog(), schemaName, null, new String[]{TABLE_TYPE, VIEW_TYPE, SYSTEM_TABLE_TYPE, SYSTEM_VIEW_TYPE})) {
            while (resultSet.next()) {

                String table = resultSet.getString(TABLE_NAME);
                if (!excludedTables.contains(table)) {
                    result.add(table);
                }
            }
        }
        return result;
    }
}
