package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.crm.enums.PeriodUnitEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

/**
 * <AUTHOR>
 * @date 2025-07-16 13:49
 * @description 周期计算服务测试类
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
public class CyclePeriodCalculateServiceImplTest {

    private CyclePeriodCalculateService cyclePeriodCalculateService;
    private static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 测试基准时间：2024年7月16日 13:49:38（周二）
    private static final long BUSINESS_TIME = 1721109578000L; // 2024-07-16 13:49:38
    private static final long START_TIME = 1719763200000L;    // 2024-06-30 00:00:00（周日）

    @BeforeEach
    void setUp() {
        cyclePeriodCalculateService = new CyclePeriodCalculateServiceImpl();
        System.out.println("=== 周期计算服务测试开始 ===");
        System.out.println("业务时间: " + formatTimestamp(BUSINESS_TIME));
        System.out.println("开始时间: " + formatTimestamp(START_TIME));
    }

    /**
     * 测试通用周期计算方法 - 所有周期单位
     */
    @Test
    void testCalculateCyclePeriod_AllPeriodUnits() {
        System.out.println("\n=== 测试通用周期计算方法 - 所有周期单位 ===");

        // 测试一次性
        testPeriodUnit(PeriodUnitEnum.ONCE, 1, "一次性");

        // 测试日周期
        testPeriodUnit(PeriodUnitEnum.DAY, 7, "7天周期");
        testPeriodUnit(PeriodUnitEnum.DAY, 30, "30天周期");

        // 测试周周期
        testPeriodUnit(PeriodUnitEnum.WEEK, 1, "1周周期");
        testPeriodUnit(PeriodUnitEnum.WEEK, 2, "2周周期");

        // 测试月周期
        testPeriodUnit(PeriodUnitEnum.MONTH, 1, "1月周期");
        testPeriodUnit(PeriodUnitEnum.MONTH, 3, "3月周期");

        // 测试季度周期
        testPeriodUnit(PeriodUnitEnum.QUARTER, 1, "1季度周期");
        testPeriodUnit(PeriodUnitEnum.QUARTER, 2, "2季度周期");

        // 测试年周期
        testPeriodUnit(PeriodUnitEnum.YEAR, 1, "1年周期");
        testPeriodUnit(PeriodUnitEnum.YEAR, 2, "2年周期");
    }

    /**
     * 测试日周期计算
     */
    @Test
    void testCalculateDayCyclePeriod() {
        System.out.println("\n=== 测试日周期计算 ===");

        // 测试1天间隔
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        validateSuccessResult(result, "1天间隔");

        // 测试7天间隔
        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 7);
        validateSuccessResult(result, "7天间隔");

        // 测试30天间隔
        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 30);
        validateSuccessResult(result, "30天间隔");

        // 测试无效参数
        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 0);
        validateFailureResult(result, "0天间隔（无效）");

        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, -1);
        validateFailureResult(result, "-1天间隔（无效）");
    }

    /**
     * 测试周周期计算
     */
    @Test
    void testCalculateWeekCyclePeriod() {
        System.out.println("\n=== 测试周周期计算 ===");

        // 测试1周间隔
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateWeekCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        validateSuccessResult(result, "1周间隔");

        // 测试2周间隔
        result = cyclePeriodCalculateService.calculateWeekCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 2);
        validateSuccessResult(result, "2周间隔");

        // 测试4周间隔
        result = cyclePeriodCalculateService.calculateWeekCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 4);
        validateSuccessResult(result, "4周间隔");

        // 测试无效参数
        result = cyclePeriodCalculateService.calculateWeekCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 0);
        validateFailureResult(result, "0周间隔（无效）");
    }

    /**
     * 测试周周期计算的详细验证
     * 验证自然周的计算逻辑
     */
    @Test
    void testCalculateWeekCyclePeriod_DetailedVerification() {
        System.out.println("\n=== 测试周周期计算的详细验证 ===");

        // 使用更清晰的测试数据
        // 开始时间：2024年7月1日（周一）00:00:00
        long startTimeMonday = 1719763200000L; // 2024-07-01 00:00:00 (周一)
        // 业务时间：2024年7月16日（周二）13:49:38
        long businessTimeTuesday = 1721109578000L; // 2024-07-16 13:49:38 (周二)

        System.out.println("测试数据说明:");
        System.out.println("- 开始时间: " + formatTimestamp(startTimeMonday) + " (周一)");
        System.out.println("- 业务时间: " + formatTimestamp(businessTimeTuesday) + " (周二)");
        System.out.println("- 预期: 业务时间在第3个自然周期中（7月15日周一到7月21日周日）");

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateWeekCyclePeriod(
                DEFAULT_TIMEZONE, businessTimeTuesday, startTimeMonday, 1);

        System.out.println("计算结果:");
        printCyclePeriodResult(result);

        if (result.isSuccess()) {
            // 验证周期索引
            System.out.println("周期验证:");
            System.out.println("- 第0周期: 2024-07-01(周一) 到 2024-07-07(周日)");
            System.out.println("- 第1周期: 2024-07-08(周一) 到 2024-07-14(周日)");
            System.out.println("- 第2周期: 2024-07-15(周一) 到 2024-07-21(周日) ← 业务时间在这里");

            assertEquals(2L, result.getCycleIndex(), "业务时间应该在第2个周期（从0开始）");

            // 验证周期时间范围
            long expectedStartTime = 1721059200000L; // 2024-07-15 00:00:00 (周一)
            long expectedEndTime = 1721577599999L;   // 2024-07-21 23:59:59 (周日)

            assertEquals(expectedStartTime, result.getCycleStartTime(), "周期开始时间应该是7月15日00:00:00");
            assertEquals(expectedEndTime, result.getCycleEndTime(), "周期结束时间应该是7月21日23:59:59");

            System.out.println("✅ 周周期计算验证通过！");
        } else {
            fail("周周期计算应该成功: " + result.getErrorMessage());
        }
    }

    /**
     * 测试月周期计算
     */
    @Test
    void testCalculateMonthCyclePeriod() {
        System.out.println("\n=== 测试月周期计算 ===");

        // 测试1月间隔
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        validateSuccessResult(result, "1月间隔");

        // 测试3月间隔
        result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 3);
        validateSuccessResult(result, "3月间隔");

        // 测试6月间隔
        result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 6);
        validateSuccessResult(result, "6月间隔");

        // 测试12月间隔
        result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 12);
        validateSuccessResult(result, "12月间隔");

        // 测试无效参数
        result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 0);
        validateFailureResult(result, "0月间隔（无效）");
    }

    /**
     * 测试季度周期计算
     */
    @Test
    void testCalculateQuarterCyclePeriod() {
        System.out.println("\n=== 测试季度周期计算 ===");

        // 测试1季度间隔
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateQuarterCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        validateSuccessResult(result, "1季度间隔");

        // 测试2季度间隔
        result = cyclePeriodCalculateService.calculateQuarterCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 2);
        validateSuccessResult(result, "2季度间隔");

        // 测试4季度间隔
        result = cyclePeriodCalculateService.calculateQuarterCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 4);
        validateSuccessResult(result, "4季度间隔");

        // 测试无效参数
        result = cyclePeriodCalculateService.calculateQuarterCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 0);
        validateFailureResult(result, "0季度间隔（无效）");
    }

    /**
     * 测试年周期计算
     */
    @Test
    void testCalculateYearCyclePeriod() {
        System.out.println("\n=== 测试年周期计算 ===");

        // 测试1年间隔
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateYearCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        validateSuccessResult(result, "1年间隔");

        // 测试2年间隔
        result = cyclePeriodCalculateService.calculateYearCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 2);
        validateSuccessResult(result, "2年间隔");

        // 测试3年间隔
        result = cyclePeriodCalculateService.calculateYearCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 3);
        validateSuccessResult(result, "3年间隔");

        // 测试无效参数
        result = cyclePeriodCalculateService.calculateYearCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 0);
        validateFailureResult(result, "0年间隔（无效）");
    }

    /**
     * 测试边界情况
     */
    @Test
    void testEdgeCases() {
        System.out.println("\n=== 测试边界情况 ===");

        // 测试业务时间早于开始时间
        long earlyBusinessTime = START_TIME - 86400000L; // 开始时间前一天
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, earlyBusinessTime, START_TIME, 1);

        System.out.println("业务时间早于开始时间测试:");
        printCyclePeriodResult(result);

        assertFalse(result.isSuccess(), "业务时间早于开始时间应该返回失败");
        assertTrue(result.getErrorMessage().contains("业务时间还未到达开始时间"), "错误信息应该说明业务时间未到达");

        // 测试业务时间等于开始时间
        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, START_TIME, START_TIME, 1);

        System.out.println("业务时间等于开始时间测试:");
        printCyclePeriodResult(result);

        assertTrue(result.isSuccess(), "业务时间等于开始时间应该返回成功");
        assertEquals(0L, result.getCycleIndex(), "周期索引应该为0");
        assertEquals(1L, result.getCycleNumber(), "周期编号应该为1");
    }

    /**
     * 测试不同时区
     */
    @Test
    void testDifferentTimezones() {
        System.out.println("\n=== 测试不同时区 ===");

        String[] timezones = {"Asia/Shanghai", "UTC", "America/New_York", "Europe/London"};

        for (String timezone : timezones) {
            CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                    timezone, BUSINESS_TIME, START_TIME, 7);

            System.out.println("时区 " + timezone + " 测试:");
            printCyclePeriodResult(result);

            assertNotNull(result, "结果不应该为空");
        }
    }

    // 辅助方法
    private void testPeriodUnit(PeriodUnitEnum periodUnit, int period, String description) {
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateCyclePeriod(
                periodUnit.getUnit(), DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, period);

        System.out.println(description + " 测试:");
        printCyclePeriodResult(result);

        assertNotNull(result, "结果不应该为空");

        if (periodUnit == PeriodUnitEnum.ONCE) {
            // 一次性应该使用开始时间
            assertTrue(result.isSuccess(), "一次性周期应该成功");
        } else {
            // 其他周期类型应该有具体的计算结果
            if (result.isSuccess()) {
                assertNotNull(result.getCycleIndex(), "周期索引不应该为空");
                assertNotNull(result.getCycleStartTime(), "周期开始时间不应该为空");
                assertNotNull(result.getCycleEndTime(), "周期结束时间不应该为空");
                assertTrue(result.getCycleStartTime() < result.getCycleEndTime(), "开始时间应该小于结束时间");
            }
        }
    }

    private void validateSuccessResult(CyclePeriodResultVO result, String description) {
        System.out.println(description + " 测试:");
        printCyclePeriodResult(result);

        assertTrue(result.isSuccess(), description + " 应该成功");
        assertNotNull(result.getCycleIndex(), "周期索引不应该为空");
        assertNotNull(result.getCycleStartTime(), "周期开始时间不应该为空");
        assertNotNull(result.getCycleEndTime(), "周期结束时间不应该为空");
        assertTrue(result.getCycleStartTime() < result.getCycleEndTime(), "开始时间应该小于结束时间");
        assertTrue(result.getCycleIndex() >= 0, "周期索引应该大于等于0");
        assertEquals(result.getCycleIndex() + 1, result.getCycleNumber(), "周期编号应该等于索引+1");
    }

    private void validateFailureResult(CyclePeriodResultVO result, String description) {
        System.out.println(description + " 测试:");
        printCyclePeriodResult(result);

        assertFalse(result.isSuccess(), description + " 应该失败");
        assertNotNull(result.getErrorMessage(), "错误信息不应该为空");
        assertTrue(result.getErrorMessage().contains("间隔时长必须大于0"), "错误信息应该说明间隔时长无效");
    }

    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "null";
        }
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                ZoneId.of(DEFAULT_TIMEZONE)
        );
        return dateTime.format(FORMATTER);
    }

    /**
     * 打印CyclePeriodResultVO的完整结果
     */
    private void printCyclePeriodResult(CyclePeriodResultVO result) {
        System.out.println("=== CyclePeriodResultVO 结果详情 ===");
        System.out.println("- success: " + result.getSuccess());
        System.out.println("- isSuccess(): " + result.isSuccess());

        if (result.isSuccess()) {
            System.out.println("- cycleIndex: " + result.getCycleIndex());
            System.out.println("- cycleNumber: " + result.getCycleNumber());
            System.out.println("- cycleStartTime: " + result.getCycleStartTime() + " (" + formatTimestamp(result.getCycleStartTime()) + ")");
            System.out.println("- cycleEndTime: " + result.getCycleEndTime() + " (" + formatTimestamp(result.getCycleEndTime()) + ")");
            System.out.println("- businessTime: " + result.getBusinessTime() + " (" + formatTimestamp(result.getBusinessTime()) + ")");

            // 计算周期时长
            if (result.getCycleStartTime() != null && result.getCycleEndTime() != null) {
                long cycleDuration = result.getCycleEndTime() - result.getCycleStartTime();
                long days = cycleDuration / (24 * 60 * 60 * 1000L);
                long hours = (cycleDuration % (24 * 60 * 60 * 1000L)) / (60 * 60 * 1000L);
                System.out.println("- 周期时长: " + cycleDuration + "ms (" + days + "天" + hours + "小时)");
            }

            // 计算业务时间在周期中的位置
            if (result.getBusinessTime() != null && result.getCycleStartTime() != null) {
                long businessOffset = result.getBusinessTime() - result.getCycleStartTime();
                long offsetDays = businessOffset / (24 * 60 * 60 * 1000L);
                long offsetHours = (businessOffset % (24 * 60 * 60 * 1000L)) / (60 * 60 * 1000L);
                System.out.println("- 业务时间偏移: " + businessOffset + "ms (" + offsetDays + "天" + offsetHours + "小时)");
            }
        } else {
            System.out.println("- errorMessage: " + result.getErrorMessage());
        }

        System.out.println("- toString(): " + result.toString());
        System.out.println("=== 结果详情结束 ===");
    }

    /**
     * 测试复杂场景 - 跨年跨月计算
     */
    @Test
    void testComplexScenarios() {
        System.out.println("\n=== 测试复杂场景 - 跨年跨月计算 ===");

        // 测试跨年的月周期计算
        // 开始时间：2024年11月1日，业务时间：2025年7月16日
        long startTime2024Nov = 1698768000000L; // 2023-11-01 00:00:00
        long businessTime2025Jul = BUSINESS_TIME;

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, businessTime2025Jul, startTime2024Nov, 1);

        System.out.println("跨年月周期计算（2023年11月开始，2025年7月业务时间，1月间隔）:");
        printCyclePeriodResult(result);

        if (result.isSuccess()) {
            // 验证周期计算的正确性
            assertTrue(result.getCycleIndex() > 0, "跨年计算应该有多个周期");
        }

        // 测试跨年的年周期计算
        result = cyclePeriodCalculateService.calculateYearCyclePeriod(
                DEFAULT_TIMEZONE, businessTime2025Jul, startTime2024Nov, 1);

        System.out.println("跨年年周期计算（2023年11月开始，2025年7月业务时间，1年间隔）:");
        printCyclePeriodResult(result);
    }

    /**
     * 测试周期个数配置的各种组合
     */
    @Test
    void testVariousPeriodCombinations() {
        System.out.println("\n=== 测试周期个数配置的各种组合 ===");

        // 测试各种日周期组合
        int[] dayPeriods = {1, 3, 7, 10, 15, 30, 60, 90};
        for (int period : dayPeriods) {
            testPeriodCombination(PeriodUnitEnum.DAY, period);
        }

        // 测试各种周周期组合
        int[] weekPeriods = {1, 2, 4, 8, 12, 26, 52};
        for (int period : weekPeriods) {
            testPeriodCombination(PeriodUnitEnum.WEEK, period);
        }

        // 测试各种月周期组合
        int[] monthPeriods = {1, 2, 3, 6, 12, 18, 24, 36};
        for (int period : monthPeriods) {
            testPeriodCombination(PeriodUnitEnum.MONTH, period);
        }

        // 测试各种季度周期组合
        int[] quarterPeriods = {1, 2, 4, 8};
        for (int period : quarterPeriods) {
            testPeriodCombination(PeriodUnitEnum.QUARTER, period);
        }

        // 测试各种年周期组合
        int[] yearPeriods = {1, 2, 3, 5, 10};
        for (int period : yearPeriods) {
            testPeriodCombination(PeriodUnitEnum.YEAR, period);
        }
    }

    /**
     * 测试周期连续性
     */
    @Test
    void testCycleContinuity() {
        System.out.println("\n=== 测试周期连续性 ===");

        // 测试月周期的连续性
        testCycleContinuityForPeriod(PeriodUnitEnum.MONTH, 1, "月周期连续性");

        // 测试周周期的连续性
        testCycleContinuityForPeriod(PeriodUnitEnum.WEEK, 2, "双周周期连续性");

        // 测试季度周期的连续性
        testCycleContinuityForPeriod(PeriodUnitEnum.QUARTER, 1, "季度周期连续性");
    }

    /**
     * 测试异常情况处理
     */
    @Test
    void testExceptionHandling() {
        System.out.println("\n=== 测试异常情况处理 ===");

        // 测试无效时区
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                "Invalid/Timezone", BUSINESS_TIME, START_TIME, 1);

        System.out.println("无效时区测试:");
        printCyclePeriodResult(result);

        // 测试空时区
        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                null, BUSINESS_TIME, START_TIME, 1);

        System.out.println("空时区测试:");
        printCyclePeriodResult(result);

        // 测试极大的周期数
        result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, Integer.MAX_VALUE);

        System.out.println("极大周期数测试:");
        printCyclePeriodResult(result);
    }

    /**
     * 测试性能场景
     */
    @Test
    void testPerformanceScenarios() {
        System.out.println("\n=== 测试性能场景 ===");

        long startTime = System.currentTimeMillis();

        // 批量测试多种周期计算
        for (int i = 0; i < 100; i++) {
            cyclePeriodCalculateService.calculateDayCyclePeriod(
                    DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
            cyclePeriodCalculateService.calculateWeekCyclePeriod(
                    DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
            cyclePeriodCalculateService.calculateMonthCyclePeriod(
                    DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("批量计算300次周期耗时: " + duration + "ms");
        assertTrue(duration < 5000, "批量计算应该在5秒内完成");
    }

    // 辅助方法
    private void testPeriodCombination(PeriodUnitEnum periodUnit, int period) {
        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateCyclePeriod(
                periodUnit.getUnit(), DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, period);

        System.out.println(periodUnit.getName() + period + "个周期测试: " +
                (result.isSuccess() ? "成功" : "失败"));
        printCyclePeriodResult(result);

        if (result.isSuccess()) {
            assertNotNull(result.getCycleIndex(), "周期索引不应该为空");
            assertTrue(result.getCycleIndex() >= 0, "周期索引应该大于等于0");
        }
    }

    private void testCycleContinuityForPeriod(PeriodUnitEnum periodUnit, int period, String description) {
        System.out.println(description + ":");

        // 获取当前周期
        CyclePeriodResultVO currentResult = cyclePeriodCalculateService.calculateCyclePeriod(
                periodUnit.getUnit(), DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, period);

        if (!currentResult.isSuccess()) {
            System.out.println("- 当前周期计算失败: " + currentResult.getErrorMessage());
            return;
        }

        // 计算下一个周期的业务时间（当前周期结束时间 + 1毫秒）
        long nextBusinessTime = currentResult.getCycleEndTime() + 1;

        CyclePeriodResultVO nextResult = cyclePeriodCalculateService.calculateCyclePeriod(
                periodUnit.getUnit(), DEFAULT_TIMEZONE, nextBusinessTime, START_TIME, period);

        if (nextResult.isSuccess()) {
            System.out.println("当前周期结果:");
            printCyclePeriodResult(currentResult);
            System.out.println("下一周期结果:");
            printCyclePeriodResult(nextResult);

            // 验证周期连续性
            assertEquals(currentResult.getCycleIndex() + 1, nextResult.getCycleIndex(),
                    "下一周期索引应该等于当前周期索引+1");
            assertEquals(currentResult.getCycleEndTime(), nextResult.getCycleStartTime(),
                    "当前周期结束时间应该等于下一周期开始时间");
        } else {
            System.out.println("当前周期结果:");
            printCyclePeriodResult(currentResult);
            System.out.println("下一周期计算失败:");
            printCyclePeriodResult(nextResult);
        }
    }

    /**
     * 测试特殊日期场景
     */
    @Test
    void testSpecialDateScenarios() {
        System.out.println("\n=== 测试特殊日期场景 ===");

        // 测试闰年2月29日
        long leapYearFeb29 = 1709164800000L; // 2024-02-29 00:00:00
        long businessTimeAfterLeap = 1709251200000L; // 2024-03-01 00:00:00

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, businessTimeAfterLeap, leapYearFeb29, 1);

        System.out.println("闰年2月29日测试:");
        printCyclePeriodResult(result);

        // 测试月末到月初的月周期
        long monthEndStart = 1706716800000L; // 2024-01-31 00:00:00
        long nextMonthBusiness = 1709251200000L; // 2024-03-01 00:00:00

        result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, nextMonthBusiness, monthEndStart, 1);

        System.out.println("月末开始的月周期测试:");
        printCyclePeriodResult(result);

        // 测试年末到年初的年周期
        long yearEndStart = 1703980800000L; // 2023-12-31 00:00:00
        long nextYearBusiness = 1704067200000L; // 2024-01-01 00:00:00

        result = cyclePeriodCalculateService.calculateYearCyclePeriod(
                DEFAULT_TIMEZONE, nextYearBusiness, yearEndStart, 1);

        System.out.println("年末开始的年周期测试:");
        printCyclePeriodResult(result);
    }

    /**
     * 测试周期计算的数学正确性
     */
    @Test
    void testMathematicalCorrectness() {
        System.out.println("\n=== 测试周期计算的数学正确性 ===");

        // 测试日周期的数学正确性
        testMathematicalCorrectnessForDays();

        // 测试周周期的数学正确性
        testMathematicalCorrectnessForWeeks();

        // 测试月周期的数学正确性
        testMathematicalCorrectnessForMonths();
    }

    /**
     * 测试并发安全性
     */
    @Test
    void testConcurrentSafety() {
        System.out.println("\n=== 测试并发安全性 ===");

        int threadCount = 10;
        int calculationsPerThread = 50;

        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < calculationsPerThread; j++) {
                        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 1);

                        if (!result.isSuccess()) {
                            results[threadIndex] = false;
                            return;
                        }
                    }
                    results[threadIndex] = true;
                } catch (Exception e) {
                    System.err.println("线程 " + threadIndex + " 发生异常: " + e.getMessage());
                    results[threadIndex] = false;
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                fail("线程被中断");
            }
        }

        // 检查结果
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 应该成功完成所有计算");
        }

        System.out.println("并发测试完成: " + threadCount + " 个线程，每个线程 " + calculationsPerThread + " 次计算");
    }

    // 数学正确性测试的辅助方法
    private void testMathematicalCorrectnessForDays() {
        System.out.println("日周期数学正确性测试:");

        // 测试7天周期，业务时间应该正好落在第N个周期
        long startTime = 1719763200000L; // 2024-06-30 00:00:00
        long businessTime = startTime + (7 * 24 * 60 * 60 * 1000L * 5); // 5个7天周期后

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, businessTime, startTime, 7);
        
        System.out.println("7天周期数学正确性验证:");
        printCyclePeriodResult(result);

        if (result.isSuccess()) {
            System.out.println("预期周期索引: 5, 实际周期索引: " + result.getCycleIndex());
            assertEquals(5L, result.getCycleIndex(), "7天周期计算应该准确");

            // 验证时间范围
            long expectedStartTime = startTime + (7 * 24 * 60 * 60 * 1000L * 5);
            assertEquals(expectedStartTime, result.getCycleStartTime(), "周期开始时间应该准确");
        }
    }

    private void testMathematicalCorrectnessForWeeks() {
        System.out.println("周周期数学正确性测试:");

        // 测试2周周期
        long startTime = 1719763200000L; // 2024-06-30 00:00:00 (周日)
        long businessTime = startTime + (14 * 24 * 60 * 60 * 1000L * 3); // 3个2周周期后

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateWeekCyclePeriod(
                DEFAULT_TIMEZONE, businessTime, startTime, 2);

        System.out.println("2周周期数学正确性验证:");
        printCyclePeriodResult(result);

        if (result.isSuccess()) {
            System.out.println("预期周期索引: 3, 实际周期索引: " + result.getCycleIndex());
            assertTrue(result.getCycleIndex() >= 0, "周周期计算应该有效");
        }
    }

    private void testMathematicalCorrectnessForMonths() {
        System.out.println("月周期数学正确性测试:");

        // 测试从2024年1月到2024年7月的月周期
        long startTime = 1704067200000L; // 2024-01-01 00:00:00
        long businessTime = 1719763200000L; // 2024-06-30 00:00:00

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateMonthCyclePeriod(
                DEFAULT_TIMEZONE, businessTime, startTime, 1);

        System.out.println("月周期数学正确性验证:");
        printCyclePeriodResult(result);

        if (result.isSuccess()) {
            System.out.println("从2024年1月到2024年6月，预期周期索引: 5, 实际周期索引: " + result.getCycleIndex());
            assertEquals(5L, result.getCycleIndex(), "月周期计算应该准确（1月=0，6月=5）");
        }
    }

    /**
     * 测试结果对象的完整性
     */
    @Test
    void testResultObjectCompleteness() {
        System.out.println("\n=== 测试结果对象的完整性 ===");

        CyclePeriodResultVO result = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 7);

        if (result.isSuccess()) {
            System.out.println("成功结果对象测试:");
            printCyclePeriodResult(result);

            // 验证必要字段
            assertNotNull(result.getSuccess(), "success字段不应该为空");
            assertTrue(result.getSuccess(), "success应该为true");
            assertNotNull(result.getCycleIndex(), "cycleIndex不应该为空");
            assertNotNull(result.getCycleNumber(), "cycleNumber不应该为空");
            assertNotNull(result.getCycleStartTime(), "cycleStartTime不应该为空");
            assertNotNull(result.getCycleEndTime(), "cycleEndTime不应该为空");
            assertNotNull(result.getBusinessTime(), "businessTime不应该为空");
            assertEquals(BUSINESS_TIME, result.getBusinessTime(), "businessTime应该等于输入值");

            // 验证toString方法
            String toStringResult = result.toString();
            assertNotNull(toStringResult, "toString结果不应该为空");
            assertTrue(toStringResult.contains("cycleIndex"), "toString应该包含cycleIndex");
            assertTrue(toStringResult.contains("cycleNumber"), "toString应该包含cycleNumber");
        }

        // 测试失败结果对象
        CyclePeriodResultVO failureResult = cyclePeriodCalculateService.calculateDayCyclePeriod(
                DEFAULT_TIMEZONE, BUSINESS_TIME, START_TIME, 0);

        System.out.println("失败结果对象测试:");
        printCyclePeriodResult(failureResult);

        assertNotNull(failureResult.getSuccess(), "success字段不应该为空");
        assertFalse(failureResult.getSuccess(), "success应该为false");
        assertNotNull(failureResult.getErrorMessage(), "errorMessage不应该为空");

        // 验证失败情况的toString方法
        String failureToString = failureResult.toString();
        assertNotNull(failureToString, "失败情况toString结果不应该为空");
        assertTrue(failureToString.contains("success=false"), "失败情况toString应该包含success=false");
        assertTrue(failureToString.contains("errorMessage"), "失败情况toString应该包含errorMessage");
    }
}
