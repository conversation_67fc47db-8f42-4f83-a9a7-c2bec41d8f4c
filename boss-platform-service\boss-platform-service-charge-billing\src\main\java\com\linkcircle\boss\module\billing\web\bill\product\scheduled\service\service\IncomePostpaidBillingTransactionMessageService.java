package com.linkcircle.boss.module.billing.web.bill.product.scheduled.service.service;

import com.linkcircle.boss.module.billing.api.bill.product.model.entity.PostpaidProductServiceIncomeBillDO;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;

/**
 * <AUTHOR>
 * @date 2025-07-08 11:41
 * @description RocketMQ事务消息实现
 */
public interface IncomePostpaidBillingTransactionMessageService {

    /**
     * 发送事务消息
     */
    boolean sendTransactionMessage(PostpaidProductServiceIncomeBillDO serviceIncomeBillDO,
                                   AccountSubscriptionsVO subscription,
                                   AccountSubscriptionsVO.Detail detail,
                                   AccountSubscriptionsVO.Product product,
                                   AccountSubscriptionsVO.Service service,
                                   CyclePeriodResultVO cyclePeriodResultVO);
}
