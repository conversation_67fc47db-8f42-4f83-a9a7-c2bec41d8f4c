package com.linkcircle.boss.module.charge.crm.web.resourceservice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceAddDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceEditDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.dto.ChargeResourceServiceQueryDTO;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.entity.ChargeResourceService;
import com.linkcircle.boss.module.charge.crm.web.resourceservice.model.vo.*;

import java.util.List;


/**
 * <p>
 * 资源服务基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface IChargeResourceServiceService extends IService<ChargeResourceService> {

    PageResult<ChargeResourceServiceVO> pageQuery(ChargeResourceServiceQueryDTO queryDTO);

    CommonResult<?> create(ChargeResourceServiceAddDTO addDTO);

    CommonResult<?> addVersion(Long serviceId);

    CommonResult<?> edit(ChargeResourceServiceEditDTO editDTO);

    CommonResult<?> activation(List<Long> ServiceIds);

    CommonResult<?> deactivate(List<Long> serviceId);

    CommonResult<?> archive(List<Long> serviceId);

    CommonResult<?> cancelArchive(List<Long> serviceId);

    CommonResult<?> delete(List<Long> serviceId);

    CommonResult<?> configScale(Long serviceId, Long scaleId);

    CommonResult<?> copy(Long serviceId);

    CommonResult<List<ChargeResourceServiceVersionVO>> getVersionList(Long serviceId);

    CommonResult<ChargeResourceServiceVersionInfoVO> getVersionInfoById(Long versionId);

    CommonResult<ChargeResourceServiceVersionInfoVO> getMaxVersionInfoById(Long serviceId);

    CommonResult<?> editVersionName(Long versionId, String versionName);

    CommonResult<List<ChargeResourceServiceGroupVO>> getVersionInfoListByName(String serviceName);

    CommonResult<List<ChargeResourceServiceVersionInfoVO>> getResourceServiceByCurrencyCode(String currencyCode);

    CommonResult<List<ChargeResourceServiceDropDownVO>> getAllResourceService();
}
