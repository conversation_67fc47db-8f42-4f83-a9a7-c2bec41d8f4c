package com.linkcircle.boss.module.billing.web.data.service.impl;

import com.linkcircle.boss.module.billing.web.data.model.vo.BillingCycleResultVO;
import com.linkcircle.boss.module.billing.web.data.service.BillingCycleCalculateService;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.enums.BillingCycleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2025-07-08 17:00
 * @description 出账周期计算服务实现
 */
@Slf4j
@Service
public class BillingCycleCalculateServiceImpl implements BillingCycleCalculateService {

    /**
     * 默认时区
     */
    private static final String DEFAULT_TIMEZONE = "Asia/Shanghai";

    /**
     * 出账周期格式
     */
    private static final DateTimeFormatter BILLING_CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public BillingCycleResultVO calculateBillingCycle(CustomerAccountVO accountInfo) {
        if (accountInfo == null) {
            return BillingCycleResultVO.notAllowed(null, "账户信息为空");
        }

        Long accountId = accountInfo.getAccountId();
        Integer billingCycleType = accountInfo.getBillingCycle();
        Integer billingDay = accountInfo.getBillingDay();
        String timezone = accountInfo.getTimezone();

        // 参数验证
        if (billingCycleType == null) {
            return BillingCycleResultVO.notAllowed(accountId, "出账周期类型为空");
        }

        if (billingDay == null) {
            return BillingCycleResultVO.notAllowed(accountId, "出账日配置为空");
        }

        // 获取出账周期类型枚举
        BillingCycleTypeEnum cycleTypeEnum = BillingCycleTypeEnum.getByType(billingCycleType);
        if (cycleTypeEnum == null) {
            return BillingCycleResultVO.notAllowed(accountId, "不支持的出账周期类型: " + billingCycleType);
        }

        // 设置默认时区
        if (timezone == null || timezone.trim().isEmpty()) {
            timezone = DEFAULT_TIMEZONE;
        }

        try {
            ZoneId zoneId = ZoneId.of(timezone);
            LocalDate currentDate = LocalDate.now(zoneId);
            // 根据出账周期类型计算出账信息
            BillingCycleResultVO resultVO = switch (cycleTypeEnum) {
                case MONTHLY -> calculateMonthlyBilling(accountId, currentDate, billingDay, timezone);
                case WEEKLY -> calculateWeeklyBilling(accountId, currentDate, billingDay, timezone);
                case QUARTERLY -> calculateQuarterlyBilling(accountId, currentDate, billingDay, timezone);
                case YEARLY -> calculateYearlyBilling(accountId, currentDate, billingDay, timezone);
            };
            resultVO.setCurrency(accountInfo.getCurrency());
            return resultVO;
        } catch (Exception e) {
            log.error("计算出账周期异常, accountId: {}, timezone: {}, billingDay: {}",
                    accountId, timezone, billingDay, e);
            return BillingCycleResultVO.notAllowed(accountId, "计算出账周期异常: " + e.getMessage());
        }
    }

    /**
     * 计算月结出账信息（按自然月）
     * 例如：每月1号出账，出账上个自然月（1号到月末）的账单
     */
    private BillingCycleResultVO calculateMonthlyBilling(Long accountId, LocalDate currentDate,
                                                         Integer billingDay, String timezone) {
        // 验证出账日配置
        if (billingDay < 1 || billingDay > 28) {
            return BillingCycleResultVO.notAllowed(accountId, "月结出账日配置无效，应为1-28: " + billingDay);
        }

        int currentDay = currentDate.getDayOfMonth();

        // 判断是否为出账日
        if (currentDay != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账日，配置出账日: %d, 当前日期: %d", billingDay, currentDay));
        }

        // 按自然月计算：出账上个自然月的账单（1号到月末）
        LocalDate lastMonth = currentDate.minusMonths(1);
        LocalDate billingStartDate = lastMonth.withDayOfMonth(1);
        LocalDate billingEndDate = lastMonth.withDayOfMonth(lastMonth.lengthOfMonth());

        // 转换为时间戳（开始时间为00:00:00，结束时间为23:59:59）
        LocalDateTime startDateTime = billingStartDate.atStartOfDay();
        LocalDateTime endDateTime = billingEndDate.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = billingStartDate.format(BILLING_CYCLE_FORMATTER) + "-" + billingEndDate.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("月结出账（自然月）：%s 00:00:00 至 %s 23:59:59",
                billingStartDate, billingEndDate);

        log.info("月结出账计算完成, accountId: {}, 出账范围: {}", accountId, description);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.MONTHLY.getType(), billingDay, billingCycle, timezone, description);
    }

    /**
     * 计算周结出账信息
     * 例如：每周一出账，出账上周一到上周日的账单
     */
    private BillingCycleResultVO calculateWeeklyBilling(Long accountId, LocalDate currentDate,
                                                        Integer billingDay, String timezone) {
        // 验证出账星期配置
        if (billingDay < 1 || billingDay > 7) {
            return BillingCycleResultVO.notAllowed(accountId, "周结出账星期配置无效，应为1-7: " + billingDay);
        }
        // 1=周一, 7=周日
        int currentDayOfWeek = currentDate.getDayOfWeek().getValue();

        // 判断是否为出账星期
        if (currentDayOfWeek != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账星期，配置出账星期: %d, 当前星期: %d", billingDay, currentDayOfWeek));
        }

        // 计算出账时间范围：上周的billingDay到上周的最后一天
        LocalDate lastWeekStart = currentDate.minusWeeks(1).with(java.time.DayOfWeek.of(billingDay));
        LocalDate lastWeekEnd = lastWeekStart.plusDays(6);

        // 转换为时间戳
        LocalDateTime startDateTime = lastWeekStart.atStartOfDay();
        LocalDateTime endDateTime = lastWeekEnd.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = lastWeekStart.format(BILLING_CYCLE_FORMATTER) + "-" + lastWeekEnd.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("周结出账：%s 00:00:00 至 %s 23:59:59",
                lastWeekStart, lastWeekEnd);

        log.info("周结出账计算完成, accountId: {}, 出账范围: {}", accountId, description);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.WEEKLY.getType(), billingDay, billingCycle, timezone, description);
    }

    /**
     * 计算季结出账信息
     * 例如：每季度第10天出账，出账上个季度第10天到这个季度第9天的账单
     */
    private BillingCycleResultVO calculateQuarterlyBilling(Long accountId, LocalDate currentDate,
                                                           Integer billingDay, String timezone) {
        // 验证出账日配置
        if (billingDay < 1 || billingDay > 90) {
            return BillingCycleResultVO.notAllowed(accountId, "季结出账日配置无效，应为1-90: " + billingDay);
        }

        // 获取当前季度的第一天
        int currentMonth = currentDate.getMonthValue();
        // 1, 4, 7, 10
        int quarterStartMonth = ((currentMonth - 1) / 3) * 3 + 1;
        LocalDate quarterStart = LocalDate.of(currentDate.getYear(), quarterStartMonth, 1);

        // 计算当前日期是季度中的第几天
        long dayOfQuarter = ChronoUnit.DAYS.between(quarterStart, currentDate) + 1;

        // 判断是否为出账日
        if (dayOfQuarter != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账日，配置出账日: %d, 当前季度第%d天", billingDay, dayOfQuarter));
        }

        // 计算出账时间范围：上个季度的billingDay到这个季度的billingDay-1
        LocalDate lastQuarterStart = quarterStart.minusMonths(3);
        LocalDate billingStartDate = lastQuarterStart.plusDays(billingDay - 1);
        LocalDate billingEndDate = currentDate.minusDays(1);

        // 转换为时间戳
        LocalDateTime startDateTime = billingStartDate.atStartOfDay();
        LocalDateTime endDateTime = billingEndDate.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = billingStartDate.format(BILLING_CYCLE_FORMATTER) + "-" + billingEndDate.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("季结出账：%s 00:00:00 至 %s 23:59:59",
                billingStartDate, billingEndDate);

        log.info("季结出账计算完成, accountId: {}, 出账范围: {}", accountId, description);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.QUARTERLY.getType(), billingDay, billingCycle, timezone, description);
    }

    /**
     * 计算年结出账信息（按自然年）
     * 例如：每年1月1号出账，出账上个自然年（1月1日到12月31日）的账单
     */
    private BillingCycleResultVO calculateYearlyBilling(Long accountId, LocalDate currentDate,
                                                        Integer billingDay, String timezone) {
        // 年结出账日配置：1表示1月1日，32表示2月1日，以此类推
        // 验证出账日配置（1-366，考虑闰年）
        if (billingDay < 1 || billingDay > 366) {
            return BillingCycleResultVO.notAllowed(accountId, "年结出账日配置无效，应为1-366: " + billingDay);
        }

        // 获取当前年份的第一天
        LocalDate yearStart = LocalDate.of(currentDate.getYear(), 1, 1);

        // 计算当前日期是年中的第几天
        long dayOfYear = ChronoUnit.DAYS.between(yearStart, currentDate) + 1;

        // 判断是否为出账日
        if (dayOfYear != billingDay) {
            return BillingCycleResultVO.notAllowed(accountId,
                    String.format("当前不是出账日，配置出账日: %d, 当前年第%d天", billingDay, dayOfYear));
        }

        // 检查闰年情况
        boolean isLeapYear = currentDate.isLeapYear();
        if (!isLeapYear && billingDay > 365) {
            return BillingCycleResultVO.notAllowed(accountId, "非闰年，跳过第" + billingDay + "天的年结出账");
        }

        // 按自然年计算：出账上个自然年的账单（1月1日到12月31日）
        int lastYear = currentDate.getYear() - 1;
        LocalDate billingStartDate = LocalDate.of(lastYear, 1, 1);
        LocalDate billingEndDate = LocalDate.of(lastYear, 12, 31);

        // 转换为时间戳
        LocalDateTime startDateTime = billingStartDate.atStartOfDay();
        LocalDateTime endDateTime = billingEndDate.atTime(LocalTime.MAX);

        ZoneId zoneId = ZoneId.of(timezone);
        long startTimestamp = startDateTime.atZone(zoneId).toInstant().toEpochMilli();
        long endTimestamp = endDateTime.atZone(zoneId).toInstant().toEpochMilli();

        String billingCycle = billingStartDate.format(BILLING_CYCLE_FORMATTER) + "-" + billingEndDate.format(BILLING_CYCLE_FORMATTER);
        String description = String.format("年结出账（自然年）：%s 00:00:00 至 %s 23:59:59",
                billingStartDate, billingEndDate);

        log.info("年结出账计算完成, accountId: {}, 出账范围: {}", accountId, description);

        return BillingCycleResultVO.allowed(accountId, startTimestamp, endTimestamp,
                BillingCycleTypeEnum.YEARLY.getType(), billingDay, billingCycle, timezone, description);
    }
}