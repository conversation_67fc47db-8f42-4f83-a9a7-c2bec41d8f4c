package com.linkcircle.boss.module.billing.web.detail.rate.strategy.context;

import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24 13:43
 * @description 原价计算请求
 */
@Data
public class OriginalPriceCalculateRequest {

    /**
     * 开始订阅时间
     */
    private Long startTime;

    /**
     * 结束订阅时间
     */
    private Long endTime;

    /**
     * 当前周期开始时间戳
     */
    private Long cycleStartTime;

    /**
     * 当前周期结束时间戳
     */
    private Long cycleEndTime;

    /**
     * 是否按比例计算
     */
    private Integer byProportion;

    /**
     * 免费试用天数
     */
    private Integer freeTryoutDays;

    /**
     * 是否计算税率
     */
    private Boolean calculateTaxEnabled = false;

    /**
     * 订阅税率
     */
    private BigDecimal taxRate;

    /**
     * 包含本次用量的总累计用量（用于阶梯和套餐计费）
     */
    private BigDecimal totalUsageWithCurrent;

    /**
     * 之前的累计用量（用于阶梯和套餐计费）
     */
    private BigDecimal previousUsage;

    /**
     * 当前用量（本次用量）
     */
    private BigDecimal currentUsage;

    /**
     * 当前用量单位（用于按量计费, 套餐计费）
     */
    private String currentUsageUnit;

    /**
     * 币种（用于按量计费）
     */
    private String currency;

    /**
     * 支付方式 0-现金 1-积分
     */
    private Integer paymentOptions;

    /**
     * 费率配置对象（具体类型由策略决定）
     */
    private Object rateConfig;

    /**
     * 套餐配置对象（仅套餐计费使用）
     */
    private Object packageConfig;

    /**
     * 优惠信息
     */
    private List<Coupon> couponList;
}
