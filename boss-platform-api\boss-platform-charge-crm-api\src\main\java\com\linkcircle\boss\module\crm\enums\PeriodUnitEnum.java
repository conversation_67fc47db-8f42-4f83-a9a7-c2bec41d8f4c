package com.linkcircle.boss.module.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:47
 * @description 间隔时长单位, 0：一次性, 1：日，2：周，3：月，4：季度，5：年
 */
@Getter
@AllArgsConstructor
public enum PeriodUnitEnum {

    ONCE(0, "一次性"),

    DAY(1, "日"),

    WEEK(2, "周"),

    MONTH(3, "月"),

    QUARTER(4, "季度"),

    YEAR(5, "年");

    private final Integer unit;

    private final String name;

    public static PeriodUnitEnum getByUnit(Integer unit) {
        for (PeriodUnitEnum value : values()) {
            if (value.unit.equals(unit)) {
                return value;
            }
        }
        return PeriodUnitEnum.ONCE;
    }

}
